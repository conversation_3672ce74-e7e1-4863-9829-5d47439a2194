import React from 'react';
import Donut<PERSON>hart from './DonutChart';
import <PERSON><PERSON>hart from './BarChart';
import './ChartDisplay.css';

const ChartDisplay = ({ charts }) => {
  console.log('🎨 ChartDisplay received charts:', charts);

  if (!charts || !Array.isArray(charts) || charts.length === 0) {
    console.log('❌ ChartDisplay: No charts to display');
    return null;
  }

  console.log('✅ ChartDisplay: Rendering', charts.length, 'chart(s)');

  const renderChart = (chart, index) => {
    console.log(`🎨 Rendering chart ${index}:`, chart);
    const { type, data, title, options = {} } = chart;

    switch (type?.toLowerCase()) {
      case 'donut':
      case 'doughnut':
      case 'pie':
        return (
          <DonutChart
            key={index}
            data={data}
            title={title}
            width={options.width || 400}
            height={options.height || 400}
            showLegend={options.showLegend !== false}
            showTooltips={options.showTooltips !== false}
            colors={options.colors}
          />
        );
      
      case 'bar':
      case 'column':
        return (
          <BarChart
            key={index}
            data={data}
            title={title}
            width={options.width || 600}
            height={options.height || 400}
            showLegend={options.showLegend !== false}
            showTooltips={options.showTooltips !== false}
            horizontal={options.horizontal || false}
            colors={options.colors}
          />
        );
      
      default:
        return (
          <div key={index} className="chart-error">
            <p>📊 Unsupported chart type: {type}</p>
            <p>Supported types: donut, pie, bar, column</p>
          </div>
        );
    }
  };

  return (
    <div className="chart-display">
      <h3 className="charts-title">📊 Charts & Visualizations</h3>
      <div className="charts-container">
        {charts.map((chart, index) => renderChart(chart, index))}
      </div>
    </div>
  );
};

export default ChartDisplay;
