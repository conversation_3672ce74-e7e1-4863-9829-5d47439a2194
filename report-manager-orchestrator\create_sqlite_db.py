#!/usr/bin/env python3
"""
Create SQLite database with sample eInvoicing tax code data
"""

import sqlite3
import os
from pathlib import Path

def create_sqlite_database():
    """Create SQLite database with sample tax code data"""
    
    # Create data directory if it doesn't exist
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # Database file path
    db_path = data_dir / "invoicing.db"
    
    # Remove existing database if it exists
    if db_path.exists():
        os.remove(db_path)
        print(f"🗑️ Removed existing database: {db_path}")
    
    # Create new database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f"📊 Creating SQLite database: {db_path}")
    
    # Create eInvoicing schema (SQLite doesn't have schemas, so we'll use table prefixes)
    # Create TaxCodeLookup table
    cursor.execute("""
        CREATE TABLE eInvoicing_TaxCodeLookup (
            TaxCodeID INTEGER PRIMARY KEY AUTOINCREMENT,
            TaxCode VARCHAR(10) NOT NULL UNIQUE,
            TaxName VARCHAR(100) NOT NULL,
            TaxRate DECIMAL(5,2) NOT NULL,
            TaxType VARCHAR(20) NOT NULL,
            Description TEXT,
            IsActive BOOLEAN DEFAULT 1,
            CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
            UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Insert sample tax code data
    sample_tax_codes = [
        ('GST0', 'Goods and Services Tax - 0%', 0.00, 'GST', 'Zero-rated GST for essential items', 1),
        ('GST5', 'Goods and Services Tax - 5%', 5.00, 'GST', 'Reduced rate GST for basic necessities', 1),
        ('GST12', 'Goods and Services Tax - 12%', 12.00, 'GST', 'Standard rate GST for most goods', 1),
        ('GST18', 'Goods and Services Tax - 18%', 18.00, 'GST', 'Higher rate GST for luxury items', 1),
        ('GST28', 'Goods and Services Tax - 28%', 28.00, 'GST', 'Highest rate GST for luxury and sin goods', 1),
        ('IGST0', 'Integrated GST - 0%', 0.00, 'IGST', 'Zero-rated IGST for inter-state transactions', 1),
        ('IGST5', 'Integrated GST - 5%', 5.00, 'IGST', 'Reduced rate IGST for inter-state transactions', 1),
        ('IGST12', 'Integrated GST - 12%', 12.00, 'IGST', 'Standard rate IGST for inter-state transactions', 1),
        ('IGST18', 'Integrated GST - 18%', 18.00, 'IGST', 'Higher rate IGST for inter-state transactions', 1),
        ('IGST28', 'Integrated GST - 28%', 28.00, 'IGST', 'Highest rate IGST for inter-state transactions', 1),
        ('CGST2.5', 'Central GST - 2.5%', 2.50, 'CGST', 'Central component of 5% GST', 1),
        ('CGST6', 'Central GST - 6%', 6.00, 'CGST', 'Central component of 12% GST', 1),
        ('CGST9', 'Central GST - 9%', 9.00, 'CGST', 'Central component of 18% GST', 1),
        ('CGST14', 'Central GST - 14%', 14.00, 'CGST', 'Central component of 28% GST', 1),
        ('SGST2.5', 'State GST - 2.5%', 2.50, 'SGST', 'State component of 5% GST', 1),
        ('SGST6', 'State GST - 6%', 6.00, 'SGST', 'State component of 12% GST', 1),
        ('SGST9', 'State GST - 9%', 9.00, 'SGST', 'State component of 18% GST', 1),
        ('SGST14', 'State GST - 14%', 14.00, 'SGST', 'State component of 28% GST', 1),
        ('CESS1', 'Cess - 1%', 1.00, 'CESS', 'Additional cess on specific goods', 1),
        ('CESS2', 'Cess - 2%', 2.00, 'CESS', 'Additional cess on specific goods', 1),
        ('CESS5', 'Cess - 5%', 5.00, 'CESS', 'Additional cess on specific goods', 1),
        ('VAT4', 'Value Added Tax - 4%', 4.00, 'VAT', 'Legacy VAT rate (pre-GST)', 0),
        ('VAT12.5', 'Value Added Tax - 12.5%', 12.50, 'VAT', 'Legacy VAT rate (pre-GST)', 0),
        ('VAT14.5', 'Value Added Tax - 14.5%', 14.50, 'VAT', 'Legacy VAT rate (pre-GST)', 0),
        ('EXEMPT', 'Tax Exempt', 0.00, 'EXEMPT', 'Goods/services exempt from tax', 1),
        ('NIL', 'Nil Rated', 0.00, 'NIL', 'Nil rated goods/services', 1),
        ('TCS0.1', 'Tax Collected at Source - 0.1%', 0.10, 'TCS', 'TCS on sale of goods', 1),
        ('TCS1', 'Tax Collected at Source - 1%', 1.00, 'TCS', 'TCS on sale of goods above threshold', 1),
        ('TDS1', 'Tax Deducted at Source - 1%', 1.00, 'TDS', 'TDS on payments', 1),
        ('TDS2', 'Tax Deducted at Source - 2%', 2.00, 'TDS', 'TDS on contractor payments', 1),
        ('TDS5', 'Tax Deducted at Source - 5%', 5.00, 'TDS', 'TDS on professional fees', 1),
        ('TDS10', 'Tax Deducted at Source - 10%', 10.00, 'TDS', 'TDS on rent payments', 1),
    ]
    
    cursor.executemany("""
        INSERT INTO eInvoicing_TaxCodeLookup 
        (TaxCode, TaxName, TaxRate, TaxType, Description, IsActive)
        VALUES (?, ?, ?, ?, ?, ?)
    """, sample_tax_codes)
    
    # Create an index for better performance
    cursor.execute("CREATE INDEX idx_taxcode ON eInvoicing_TaxCodeLookup(TaxCode)")
    cursor.execute("CREATE INDEX idx_taxtype ON eInvoicing_TaxCodeLookup(TaxType)")
    cursor.execute("CREATE INDEX idx_active ON eInvoicing_TaxCodeLookup(IsActive)")
    
    # Commit and close
    conn.commit()
    
    # Verify data
    cursor.execute("SELECT COUNT(*) FROM eInvoicing_TaxCodeLookup")
    count = cursor.fetchone()[0]
    
    cursor.execute("SELECT TaxCode, TaxName, TaxRate FROM eInvoicing_TaxCodeLookup LIMIT 5")
    sample_rows = cursor.fetchall()
    
    conn.close()
    
    print(f"✅ Database created successfully!")
    print(f"📊 Inserted {count} tax codes")
    print(f"📁 Database location: {db_path.absolute()}")
    print(f"🔍 Sample data:")
    for row in sample_rows:
        print(f"   {row[0]} - {row[1]} ({row[2]}%)")
    
    return str(db_path.absolute())

if __name__ == "__main__":
    db_path = create_sqlite_database()
    print(f"\n🎉 SQLite database ready at: {db_path}")
