<!DOCTYPE html>
<html>
<head>
    <title>Test Streaming API</title>
</head>
<body>
    <h1>Test Streaming API</h1>
    <button onclick="testStream()">Test Stream Query</button>
    <div id="result"></div>

    <script>
        async function testStream() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Starting stream test...';
            
            try {
                console.log('🧪 Testing stream with minimal request...');
                
                const response = await fetch('http://localhost:8007/query/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: "I need all tax code from einvoicing",
                        output_format: "excel",
                        chunk_size: 10
                    })
                });

                console.log('📡 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                let excelData = {
                    metadata: null,
                    sheets: []
                };
                
                let receivedChunks = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                console.log('📦 Received:', data.type, data);
                                
                                switch (data.type) {
                                    case 'metadata':
                                        resultDiv.innerHTML += '<p>✅ Metadata received</p>';
                                        break;
                                        
                                    case 'excel_metadata':
                                        excelData.metadata = data;
                                        resultDiv.innerHTML += '<p>✅ Excel metadata received</p>';
                                        break;
                                        
                                    case 'sheet_info':
                                        excelData.sheets[data.sheet_index] = {
                                            name: data.sheet_name,
                                            headers: data.headers,
                                            rows: [],
                                            totalRows: data.total_rows
                                        };
                                        resultDiv.innerHTML += `<p>✅ Sheet info: ${data.sheet_name} (${data.total_rows} rows)</p>`;
                                        break;
                                        
                                    case 'sheet_data':
                                        excelData.sheets[data.sheet_index].rows.push(...data.rows);
                                        receivedChunks++;
                                        resultDiv.innerHTML += `<p>📊 Data chunk ${data.chunk_index + 1}/${data.total_chunks} (${data.chunk_size} rows)</p>`;
                                        break;
                                        
                                    case 'complete':
                                        resultDiv.innerHTML += '<p>🎉 <strong>STREAM COMPLETE!</strong></p>';
                                        resultDiv.innerHTML += `<p>📊 Total chunks received: ${receivedChunks}</p>`;
                                        resultDiv.innerHTML += `<p>📋 Total rows: ${excelData.sheets[0]?.rows.length || 0}</p>`;
                                        resultDiv.innerHTML += `<p>🎯 Sheet name: ${excelData.sheets[0]?.name || 'N/A'}</p>`;
                                        
                                        // Show sample data
                                        if (excelData.sheets[0]?.rows.length > 0) {
                                            const sampleRow = excelData.sheets[0].rows[0];
                                            resultDiv.innerHTML += `<p>📄 Sample row: ${JSON.stringify(sampleRow)}</p>`;
                                        }
                                        
                                        console.log('🎉 Final Excel Data:', excelData);
                                        return;
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse line:', line, parseError);
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('❌ Stream test failed:', error);
                resultDiv.innerHTML += `<p style="color: red;">❌ Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
