.chart-display {
  margin: 20px 0;
  padding: 0;
}

.charts-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

/* Grid layout for multiple charts */
.charts-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  padding: 0 20px;
}

.chart-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
  color: #6c757d;
  font-size: 16px;
  margin: 20px;
  text-align: center;
}

.chart-error p {
  margin: 5px 0;
}

.chart-error p:first-child {
  font-weight: 600;
  color: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
  .charts-title {
    font-size: 18px;
    padding: 0 15px;
  }
  
  .charts-container.grid {
    grid-template-columns: 1fr;
    padding: 0 15px;
  }
  
  .chart-error {
    margin: 15px;
    padding: 30px 20px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .charts-title {
    font-size: 16px;
    padding: 0 10px;
  }
  
  .charts-container.grid {
    padding: 0 10px;
  }
  
  .chart-error {
    margin: 10px;
    padding: 25px 15px;
    font-size: 13px;
  }
}

/* Animation */
.chart-display {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
