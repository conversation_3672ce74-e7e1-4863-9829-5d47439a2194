import { useState, useRef, useEffect } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import streamingApi from '../services/streamingApi';
import ResponseRenderer from './ResponseRenderer/ResponseRenderer';
import DownloadRenderer from './DownloadRenderer/DownloadRenderer';
import PowerBIEmbed from './PowerBI/PowerBIEmbed';
import ExcelGenerator from '../utils/excelGenerator';
import EmailReportModal from './EmailReportModal/EmailReportModal';
import ProgressOverlay from './ProgressOverlay/ProgressOverlay';
import authService from '../services/authService';
import chartService from '../services/chartService';
import powerBIService from '../services/powerBIService';
import ChartTest from './Charts/ChartTest';
import LoadingAnimation from './LoadingAnimation/LoadingAnimation';
import ExcelChartGenerator from '../utils/excelChartGenerator';
import EnhancedExcelGenerator from '../utils/enhancedExcelGenerator';
import ChartLinkGenerator from '../utils/chartLinkGenerator';
import '../assets/Style/ChatWindow.css';
import '../assets/Style/WelcomeScreen.css';
import './ProgressOverlay/ProgressOverlay.css';

export default function ChatWindow({ conversation, onSendMessage }) {
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [editingMessageId, setEditingMessageId] = useState(null);
  const [editingText, setEditingText] = useState('');
  const [powerBIReports, setPowerBIReports] = useState({});
  const [showProgressOverlay, setShowProgressOverlay] = useState(false);
  const [pendingPowerBIData, setPendingPowerBIData] = useState(null);
  const [pendingRequestData, setPendingRequestData] = useState(null);
  const [thinkingMessageId, setThinkingMessageId] = useState(null);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);
  const thinkingTimerRef = useRef(null);

  // Get current authenticated user
  const currentUser = authService.getCurrentUser() || {
    email: '<EMAIL>',
    name: 'John Doe'
  };

  // Debug logging
  console.log('ChatWindow rendered with conversation:', conversation);
  console.log('Conversation messages count:', conversation?.messages?.length || 0);
  
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation?.messages]);

  useEffect(() => {
    if (transcript) {
      setMessage(transcript);
    }
  }, [transcript]);

  // Cleanup thinking timer on unmount
  useEffect(() => {
    return () => {
      if (thinkingTimerRef.current) {
        clearTimeout(thinkingTimerRef.current);
      }
    };
  }, []);

  // Auto-resize textarea
  const handleTextareaChange = (e) => {
    setMessage(e.target.value);
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  };

  // Function to normalize numbers with commas for backend processing
  const normalizeNumbersInText = (text) => {
    // Enhanced pattern to match various number formats with commas
    // Matches patterns like: 1,00,000 (Indian) or 1,000,000 (International) or 10,00,000
    // Also handles decimal numbers like 1,00,000.50
    const numberWithCommasPattern = /\b\d{1,3}(?:,\d{2,3})*(?:\.\d+)?\b/g;

    return text.replace(numberWithCommasPattern, (match) => {
      // Check if this looks like a valid number with commas
      const hasValidCommaPattern = /^\d{1,3}(?:,\d{2,3})+/.test(match);

      if (hasValidCommaPattern) {
        // Remove all commas and return the clean number
        const cleanNumber = match.replace(/,/g, '');
        console.log(`Normalized number: ${match} -> ${cleanNumber}`);
        return cleanNumber;
      }

      // Return original if it doesn't match expected comma patterns
      return match;
    });
  };

  const handleSend = async () => {
    console.log('🚀 handleSend called with message:', message);

    if (message.trim() && !isLoading) {
      const userMessage = message.trim();
      console.log('✅ Message validation passed, processing:', userMessage);

      // Don't clear message immediately - wait for successful send
      resetTranscript();

      try {
        setIsLoading(true);
        console.log('🔄 Loading state set to true');

        // Add user message to chat immediately with proper structure
        const userMessageObj = {
          id: 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
          text: userMessage,
          timestamp: new Date(),
          success: true,
          isStructured: false,
          originalQuery: userMessage
        };

        console.log('📤 Adding user message:', userMessageObj);
        onSendMessage(userMessageObj, 'user');
        console.log('✅ User message sent to parent');

        // Clear message only after successfully adding to conversation
        setMessage('');
        console.log('🧹 Input cleared');

        // Small delay to ensure user message is processed before API call
        console.log('⏳ Waiting 50ms for user message processing...');
        await new Promise(resolve => setTimeout(resolve, 50));

        // Call the streaming API via service
        // Normalize numbers with commas before sending to backend
        let normalizedMessage;
        try {
          normalizedMessage = normalizeNumbersInText(userMessage);
          console.log('📝 Original message:', userMessage);
          console.log('🔄 Normalized message for API:', normalizedMessage);
        } catch (normError) {
          console.error('❌ Error normalizing message:', normError);
          normalizedMessage = userMessage; // Fallback to original message
        }

        // Show thinking message for every request
        const thinkingId = 'thinking_' + Date.now();
        const thinkingMessage = {
          id: thinkingId,
          text: '',
          timestamp: new Date(),
          success: true,
          isStructured: false,
          showThinking: true,
          originalQuery: userMessage
        };
        console.log('🤔 Creating thinking message:', thinkingMessage);
        console.log('📤 Sending thinking message to parent...');
        onSendMessage(thinkingMessage, 'assistant');
        console.log('✅ Thinking message sent successfully');

        // Store thinking message ID and request data
        console.log('💾 Storing thinking message ID and request data...');
        setThinkingMessageId(thinkingId);
        setPendingRequestData({ userMessage, normalizedMessage });

        // Set timer to complete thinking after 13 seconds
        console.log('⏰ Setting 13-second thinking timer...');
        thinkingTimerRef.current = setTimeout(() => {
          console.log('⏰ Thinking timer completed - calling handleThinkingComplete');
          handleThinkingComplete();
        }, 13000);
        console.log('✅ Timer set successfully');

        // TEMPORARY: Skip API call and use mock result for testing thinking message
        console.log('🧪 Using mock API call for testing...');
        setTimeout(() => {
          const mockResult = {
            success: true,
            message: 'Mock response: Here are all active employees',
            downloadInfo: null
          };
          console.log('🧪 Mock result ready:', mockResult);
          setPendingRequestData(prev => ({ ...prev, result: mockResult }));
        }, 2000); // Mock API delay

        // The progress overlay will handle the result processing when it completes

      } catch (error) {
        console.error('❌ Critical error in handleSend:', error);
        console.error('❌ Error stack:', error.stack);

        // Clear any pending timers
        if (thinkingTimerRef.current) {
          clearTimeout(thinkingTimerRef.current);
          thinkingTimerRef.current = null;
        }

        const errorMessage = {
          id: 'error_' + Date.now(),
          text: 'Sorry, I encountered an error processing your request. Please try again.',
          timestamp: new Date(),
          success: false
        };
        console.log('📤 Sending error message:', errorMessage);
        onSendMessage(errorMessage, 'assistant');
      } finally {
        console.log('🏁 handleSend finally block - setting loading to false');
        setIsLoading(false);
      }
    } else {
      console.log('⚠️ handleSend skipped - message empty or already loading:', {
        messageEmpty: !message.trim(),
        isLoading
      });
    }
  };

  // Test function to create a mock Excel download with exact backend format
  const addTestDownload = () => {
    try {
      console.log('Creating test download with exact backend format...');

      // Create test data matching exact backend response format
      const testStreamingData = {
        sheets: {
          "Sales Data": {
            headers: ["product", "sales", "revenue", "month"],
            rows: [
              ["Product A", 1200, 24000, "January"],
              ["Product B", 850, 17000, "January"],
              ["Product C", 950, 19000, "January"],
              ["Product A", 1350, 27000, "February"],
              ["Product B", 920, 18400, "February"],
              ["Product C", 1100, 22000, "February"]
            ]
          }
        },
        metadata: {
          title: "Test Sales Data Report",
          generated_at: new Date().toISOString()
        }
      };

      console.log('Test streaming data:', testStreamingData);

      // Generate Excel file using the same method as real data
      const filename = `Report__Test_Sales_Data__${Date.now()}.xlsx`;
      const excelInfo = ExcelGenerator.generateFromApiResponse(
        { streamingData: testStreamingData },
        filename
      );

      console.log('Test Excel generation result:', excelInfo);

      const downloadMessage = {
        text: `📊 Report generated successfully!\n\n📋 **Report: Test Sales Data**\n\nFile: ${excelInfo.filename}\nSize: ${(excelInfo.size / 1024).toFixed(1)} KB`,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        originalQuery: "Test Sales Data",
        downloadInfo: excelInfo
      };

      console.log('Sending test download message:', downloadMessage);
      onSendMessage("Generate test Excel download", 'user');
      onSendMessage(downloadMessage, 'assistant');
    } catch (error) {
      console.error('Test Excel generation failed:', error);
      onSendMessage("Generate test Excel download", 'user');
      onSendMessage({
        text: `❌ Test Excel generation failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test function to add sample structured data
  const addTestData = () => {
    const testData = {
      "success": true,
      "result": {
        "success": true,
        "data": {
          "reports": [
            {"id": 1, "title": "Q1 Sales Report", "type": "sales", "date": "2024-01-15", "status": "completed"},
            {"id": 2, "title": "Marketing Analysis", "type": "marketing", "date": "2024-02-01", "status": "draft"}
          ],
          "metrics": [
            {"name": "revenue", "value": 150000, "period": "Q1", "trend": "up"},
            {"name": "customers", "value": 1250, "period": "Q1", "trend": "up"}
          ]
        },
        "query_type": "data_retrieval",
        "metadata": {"entities_found": ["sales data"], "data_sources_accessed": ["reports", "metrics"]},
        "error": null
      },
      "context": {"intent": "view_sales_data", "task_type": "query", "entities": ["sales data"], "confidence": 0.9, "parameters": {}},
      "agents_used": ["query_agent"],
      "execution_time": 4.6275827999998,
      "flow_id": null,
      "error": null
    };

    const assistantMessage = {
      text: testData,
      agents_used: ["query_agent"],
      timestamp: new Date(),
      success: true,
      isStructured: true,
      originalQuery: "test structured data"
    };

    onSendMessage("Show me test structured data", 'user');
    onSendMessage(assistantMessage, 'assistant');
  };

  // Test API connection directly
  const testApiDirect = async () => {
    try {
      console.log('Testing direct API call...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'test query for debugging',
          output_format: 'json'
        })
      });

      console.log('Direct API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const text = await response.text();
      console.log('Raw response text:', text);

      try {
        const json = JSON.parse(text);
        console.log('Parsed JSON:', json);

        onSendMessage("Direct API test", 'user');
        onSendMessage({
          text: `✅ Direct API test successful!\n\nResponse: ${JSON.stringify(json, null, 2)}`,
          timestamp: new Date(),
          success: true,
          isStructured: false
        }, 'assistant');
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        onSendMessage("Direct API test", 'user');
        onSendMessage({
          text: `❌ API returned non-JSON response:\n\n${text}`,
          timestamp: new Date(),
          success: false,
          isStructured: false
        }, 'assistant');
      }
    } catch (error) {
      console.error('Direct API test failed:', error);
      onSendMessage("Direct API test", 'user');
      onSendMessage({
        text: `❌ API test failed: ${error.message}`,
        timestamp: new Date(),
        success: false,
        isStructured: false
      }, 'assistant');
    }
  };

  // Generate Excel from last API response
  const generateExcelFromLastResponse = () => {
    try {
      // Find the last assistant message with structured data
      const lastStructuredMessage = conversation.messages
        .filter(msg => msg.type === 'assistant' && (msg.isStructured || msg.text))
        .pop();

      if (!lastStructuredMessage) {
        onSendMessage("No data available for Excel generation", 'user');
        onSendMessage({
          text: '❌ No structured data found in recent messages to generate Excel from.',
          timestamp: new Date(),
          success: false
        }, 'assistant');
        return;
      }

      // Generate Excel from the message data
      const responseData = typeof lastStructuredMessage.text === 'object'
        ? lastStructuredMessage.text
        : { message: lastStructuredMessage.text, agents_used: lastStructuredMessage.agents_used };

      const excelInfo = ExcelGenerator.generateFromApiResponse(
        responseData,
        `manual_report_${Date.now()}.xlsx`
      );

      const downloadMessage = {
        text: `📊 Excel generated from last response!\n\nFile: ${excelInfo.filename}\nSize: ${(excelInfo.size / 1024).toFixed(1)} KB`,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        downloadInfo: excelInfo
      };

      onSendMessage("Generate Excel from last response", 'user');
      onSendMessage(downloadMessage, 'assistant');

    } catch (error) {
      console.error('Manual Excel generation failed:', error);
      onSendMessage("Generate Excel from last response", 'user');
      onSendMessage({
        text: `❌ Excel generation failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test Excel generation specifically
  const testExcelGeneration = async () => {
    try {
      console.log('Testing Excel generation...');
      const response = await fetch('http://localhost:8000/query/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: 'generate excel report with sample data',
          output_format: 'excel'
        })
      });

      console.log('Excel API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      const contentType = response.headers.get('content-type');
      const contentDisposition = response.headers.get('content-disposition');

      console.log('Content analysis:', {
        contentType,
        contentDisposition,
        isExcel: contentType && contentType.includes('excel'),
        isAttachment: contentDisposition && contentDisposition.includes('attachment')
      });

      if (contentType && (contentType.includes('excel') || contentType.includes('spreadsheet'))) {
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);

        onSendMessage("Test Excel generation", 'user');
        onSendMessage({
          text: `✅ Excel file detected!\n\nSize: ${blob.size} bytes\nType: ${blob.type}`,
          timestamp: new Date(),
          success: true,
          downloadInfo: {
            downloadUrl,
            filename: 'test-report.xlsx',
            size: blob.size,
            format: 'excel'
          }
        }, 'assistant');
      } else {
        const text = await response.text();
        onSendMessage("Test Excel generation", 'user');
        onSendMessage({
          text: `❌ Expected Excel file but got:\n\nContent-Type: ${contentType}\nResponse: ${text.substring(0, 500)}...`,
          timestamp: new Date(),
          success: false
        }, 'assistant');
      }
    } catch (error) {
      console.error('Excel test failed:', error);
      onSendMessage("Test Excel generation", 'user');
      onSendMessage({
        text: `❌ Excel test failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test function for chart functionality with loading animation
  const testChartGeneration = async () => {
    try {
      console.log('🧪 Testing chart generation with loading animation...');

      // Add user message
      onSendMessage("Give me donut chart for all employee salary", 'user');

      // Show loading animation
      const loadingMessage = {
        id: 'loading_' + Date.now(),
        text: '',
        timestamp: new Date(),
        success: true,
        isStructured: false,
        showLoading: true,
        loadingMessages: [
          "🔍 Analyzing salary data...",
          "📊 Processing employee information...",
          "🎨 Generating donut chart...",
          "📋 Creating Excel report with charts...",
          "✨ Finalizing your report..."
        ],
        originalQuery: "Give me donut chart for all employee salary"
      };

      console.log('⏳ Showing loading animation...');
      onSendMessage(loadingMessage, 'assistant');

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 4000));

      // Create mock chart data (since backend might not be running)
      const mockChartData = {
        type: "donut",
        title: "Employee Salary Distribution by Department",
        data: {
          labels: ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations"],
          values: [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
          summary: [
            {"label": "Departments", "value": "6"},
            {"label": "Total Employees", "value": "150"},
            {"label": "Avg Salary", "value": "$85,000"}
          ]
        },
        options: {
          width: 500,
          height: 400
        }
      };

      console.log('📊 Mock chart data created:', mockChartData);

      // Create real Excel data for download
      const excelData = [
        {
          Department: "Engineering",
          Employee_Count: 45,
          Total_Salary: 2500000,
          Avg_Salary: 55556
        },
        {
          Department: "Sales",
          Employee_Count: 30,
          Total_Salary: 1800000,
          Avg_Salary: 60000
        },
        {
          Department: "Marketing",
          Employee_Count: 20,
          Total_Salary: 1200000,
          Avg_Salary: 60000
        },
        {
          Department: "HR",
          Employee_Count: 15,
          Total_Salary: 950000,
          Avg_Salary: 63333
        },
        {
          Department: "Finance",
          Employee_Count: 25,
          Total_Salary: 1600000,
          Avg_Salary: 64000
        },
        {
          Department: "Operations",
          Employee_Count: 15,
          Total_Salary: 1100000,
          Avg_Salary: 73333
        }
      ];

      console.log('📋 Creating Excel file with visual charts:', excelData);

      // Generate chart link since this is a chart request
      const userQuery = "Give me donut chart for all employee salary";
      const chartLink = ChartLinkGenerator.generateChartLinkIfRequested(userQuery, mockChartData);

      console.log('🔗 Chart link generated:', chartLink);

      // Generate Excel file with visual chart representations (no chart link in Excel)
      const filename = `Employee_Salary_Chart_${Date.now()}.xlsx`;
      const downloadInfo = await EnhancedExcelGenerator.generateWithVisualChart(excelData, filename, mockChartData);

      console.log('📋 Excel with visual charts created:', downloadInfo);

      const chartMessage = {
        id: 'chart_test_' + Date.now(),
        text: '📊 Chart generated successfully! Here is your employee salary donut chart:',
        timestamp: new Date(),
        success: true,
        isStructured: false,
        charts: [mockChartData],
        chartMetadata: {
          source: 'test_mock',
          generated_at: new Date().toISOString(),
          query: "Give me donut chart for all employee salary"
        },
        downloadInfo: downloadInfo,
        chartLink: chartLink, // Add chart link to message instead of Excel
        originalQuery: "Give me donut chart for all employee salary"
      };

      console.log('📤 Sending mock chart message:', chartMessage);
      console.log('📊 Charts array:', chartMessage.charts);
      console.log('📋 Download info:', chartMessage.downloadInfo);
      onSendMessage(chartMessage, 'assistant');

    } catch (error) {
      console.error('Chart test failed:', error);
      onSendMessage({
        text: `❌ Chart test failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test Power BI integration
  const testPowerBIIntegration = async () => {
    try {
      console.log('🔷 Testing Power BI integration...');

      // Mock chart data for Power BI
      const mockChartData = {
        labels: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance'],
        values: [2500000, 1800000, 1200000, 950000, 1600000],
        datasetLabel: 'Department Salary'
      };

      // Generate Power BI report
      const powerBIResult = await powerBIService.generateReport(
        mockChartData,
        {
          originalQuery: 'Show me department salary breakdown in Power BI',
          dataSource: 'employee_database',
          generatedFrom: 'test'
        },
        'Department Salary Analysis Dashboard'
      );

      if (powerBIResult.success) {
        const powerBIMessage = {
          id: 'powerbi_test_' + Date.now(),
          text: '🔷 Power BI Dashboard generated successfully! Interactive report is ready for analysis.',
          timestamp: new Date(),
          success: true,
          powerBIReport: powerBIResult.report,
          originalQuery: 'Show me department salary breakdown in Power BI'
        };

        onSendMessage("Generate Power BI dashboard for department salaries", 'user');
        onSendMessage(powerBIMessage, 'assistant');
      }

    } catch (error) {
      console.error('❌ Power BI test failed:', error);
      onSendMessage("Generate Power BI dashboard for department salaries", 'user');
      onSendMessage({
        text: `❌ Power BI test failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test function for complete workflow (chart + excel) with loading
  const testCompleteWorkflow = async () => {
    try {
      console.log('Testing complete chart + Excel workflow with loading...');

      // Add user message
      onSendMessage("Give me donut chart for all employee salary with Excel download", 'user');

      // Show loading animation
      const loadingMessage = {
        id: 'loading_complete_' + Date.now(),
        text: '',
        timestamp: new Date(),
        success: true,
        isStructured: false,
        showLoading: true,
        loadingMessages: [
          "🔍 Analyzing complete workflow request...",
          "📊 Processing employee salary data...",
          "🎨 Generating interactive donut chart...",
          "📋 Creating Excel with embedded charts...",
          "🔗 Preparing download links...",
          "✨ Finalizing complete report..."
        ],
        originalQuery: "Give me donut chart for all employee salary with Excel download"
      };

      console.log('⏳ Showing complete workflow loading...');
      onSendMessage(loadingMessage, 'assistant');

      // Simulate longer processing time for complete workflow
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Generate chart data
      const completeChartData = {
        type: "donut",
        title: "Employee Salary Distribution by Department",
        data: {
          labels: ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations"],
          values: [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
          summary: [
            {"label": "Departments", "value": "6"},
            {"label": "Total Employees", "value": "150"},
            {"label": "Avg Salary", "value": "$85,000"}
          ]
        },
        options: { width: 500, height: 400 }
      };

      // Generate chart link for complete workflow
      const completeUserQuery = "Give me donut chart for all employee salary with Excel download";
      const completeChartLink = ChartLinkGenerator.generateChartLinkIfRequested(completeUserQuery, completeChartData);

      console.log('🔗 Complete workflow chart link generated:', completeChartLink);

      // Simulate the complete workflow response
      const mockResult = {
        success: true,
        message: "📊 Chart and Excel report generated successfully!",
        charts: [completeChartData],
        downloadInfo: await EnhancedExcelGenerator.generateWithVisualChart([
          { Department: "Engineering", Employee_Count: 45, Total_Salary: 2500000, Avg_Salary: 55556 },
          { Department: "Sales", Employee_Count: 30, Total_Salary: 1800000, Avg_Salary: 60000 },
          { Department: "Marketing", Employee_Count: 20, Total_Salary: 1200000, Avg_Salary: 60000 },
          { Department: "HR", Employee_Count: 15, Total_Salary: 950000, Avg_Salary: 63333 },
          { Department: "Finance", Employee_Count: 25, Total_Salary: 1600000, Avg_Salary: 64000 },
          { Department: "Operations", Employee_Count: 15, Total_Salary: 1100000, Avg_Salary: 73333 }
        ], `Employee_Salary_Complete_${Date.now()}.xlsx`, completeChartData),
        chartLink: completeChartLink, // Add chart link to message
        chartGenerated: true,
        chartType: "donut"
      };

      const responseMessage = {
        id: 'complete_test_' + Date.now(),
        text: mockResult.message,
        timestamp: new Date(),
        success: true,
        isStructured: false,
        downloadInfo: mockResult.downloadInfo,
        charts: mockResult.charts,
        chartMetadata: {
          source: 'complete_workflow_test',
          generated_at: new Date().toISOString(),
          query: "Give me donut chart for all employee salary with Excel download"
        },
        chartGenerated: mockResult.chartGenerated,
        chartType: mockResult.chartType,
        originalQuery: "Give me donut chart for all employee salary with Excel download"
      };

      console.log('Sending complete workflow message:', responseMessage);
      onSendMessage(responseMessage, 'assistant');

    } catch (error) {
      console.error('Complete workflow test failed:', error);
      onSendMessage({
        text: `❌ Complete workflow test failed: ${error.message}`,
        timestamp: new Date(),
        success: false
      }, 'assistant');
    }
  };

  // Test Chart.js directly
  const testChartJS = () => {
    console.log('🧪 Testing Chart.js directly...');

    // Add user message
    onSendMessage("Testing Chart.js functionality", 'user');

    // Create a message with the ChartTest component
    const testMessage = {
      id: 'chartjs_test_' + Date.now(),
      text: '🧪 Testing Chart.js directly with native component:',
      timestamp: new Date(),
      success: true,
      isStructured: false,
      showChartTest: true, // Special flag for test component
      originalQuery: "Testing Chart.js functionality"
    };

    console.log('📤 Sending Chart.js test message:', testMessage);
    onSendMessage(testMessage, 'assistant');
  };

  const startListening = () => {
    setIsListening(true);
    resetTranscript();
    SpeechRecognition.startListening({ continuous: true });
  };

  const stopListening = () => {
    setIsListening(false);
    SpeechRecognition.stopListening();
  };

  // Handler functions for message actions
  const handleCopyMessage = async (text) => {
    try {
      const textToCopy = typeof text === 'string' ? text : JSON.stringify(text, null, 2);
      await navigator.clipboard.writeText(textToCopy);
      console.log('Message copied to clipboard');
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy message:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = textToCopy;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  };

  const handleThumbsUp = (messageId) => {
    console.log('Thumbs up for message:', messageId);
    // TODO: Implement feedback tracking
    // Could send feedback to analytics or backend
  };

  const handleThumbsDown = (messageId) => {
    console.log('Thumbs down for message:', messageId);
    // TODO: Implement feedback tracking and possibly show feedback form
  };

  const handleReadAloud = (text) => {
    if ('speechSynthesis' in window) {
      // Stop any ongoing speech
      window.speechSynthesis.cancel();

      // Create new utterance
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 1;

      // Speak the text
      window.speechSynthesis.speak(utterance);
      console.log('Reading aloud:', text.substring(0, 50) + '...');
    } else {
      alert('Text-to-speech is not supported in your browser');
    }
  };

  const handleEmailReport = (message) => {
    setSelectedMessage(message);
    setEmailModalOpen(true);
    console.log('Opening email modal for message:', message.id);
  };

  const handleGeneratePowerBI = async (message) => {
    try {
      console.log('🔷 Generating Power BI report for message:', message.id);

      // Extract chart data from the message
      let chartData = message.charts?.[0]?.data || message.chartData;

      // If no chart data but has downloadInfo, create mock chart data
      if (!chartData && message.downloadInfo) {
        console.log('🔷 No chart data found, creating mock data for Power BI');
        chartData = {
          labels: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations'],
          values: [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
          datasetLabel: 'Department Analysis'
        };
      }

      if (!chartData) {
        console.warn('No chart data available for Power BI generation');
        return;
      }

      // Store the data for later use and show progress overlay
      setPendingPowerBIData({ message, chartData });
      setShowProgressOverlay(true);

      // Start the actual Power BI generation in parallel (don't await)
      powerBIService.generateReport(
        chartData,
        {
          originalQuery: message.originalQuery,
          messageId: message.id,
          generatedFrom: 'chat_message',
          dataSource: message.metadata?.datasource || 'unknown'
        },
        `Report: ${message.originalQuery || 'Data Analysis'}`
      ).then(result => {
        // Store result for when overlay completes
        setPendingPowerBIData(prev => ({ ...prev, result }));
      }).catch(error => {
        // Store error for when overlay completes
        setPendingPowerBIData(prev => ({ ...prev, error }));
      });

    } catch (error) {
      console.error('❌ Error starting Power BI report generation:', error);

      // Show error immediately if setup fails
      const errorMessage = {
        id: 'powerbi_error_' + Date.now(),
        text: `❌ Error starting Power BI report generation: ${error.message}`,
        timestamp: new Date(),
        success: false,
        type: 'assistant'
      };

      onSendMessage(errorMessage, 'assistant');
    }
  };

  // Handle thinking completion (timer or skip)
  const handleThinkingComplete = () => {
    console.log('✅ Thinking complete called', {
      hasPendingData: !!pendingRequestData,
      hasResult: !!pendingRequestData?.result,
      hasError: !!pendingRequestData?.error
    });

    // Clear the thinking timer
    if (thinkingTimerRef.current) {
      clearTimeout(thinkingTimerRef.current);
      thinkingTimerRef.current = null;
    }

    setIsLoading(false);

    // Handle general request completion
    if (pendingRequestData?.result) {
      const { userMessage, result } = pendingRequestData;

      // Process the result similar to the original handleSend logic
      let enhancedResult = result;

      // Check if this is a chart request and enhance with chart data
      if (result.charts && result.charts.length > 0) {
        console.log('Charts found in streaming result:', result.charts);
        enhancedResult = {
          ...result,
          charts: result.charts,
          chartMetadata: {
            source: 'streaming_api',
            generated_at: new Date().toISOString(),
            query: userMessage,
            chart_generated: result.chartGenerated,
            chart_type: result.chartType
          }
        };
      } else if (chartService.shouldGenerateChart(userMessage)) {
        console.log('Chart request detected but no charts in streaming result, enhancing...');
        try {
          enhancedResult = chartService.enhanceResponseWithCharts(result, userMessage);
          console.log('Enhanced result with charts:', enhancedResult);
        } catch (error) {
          console.error('Error enhancing with charts:', error);
          // Continue with original result if chart enhancement fails
        }
      }

      if (enhancedResult.success) {
        // Create response message with all available data
        const responseMessage = {
          id: 'response_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
          text: enhancedResult.message || 'Report generated successfully!',
          timestamp: new Date(),
          success: true,
          isStructured: false,
          downloadInfo: enhancedResult.downloadInfo,
          metadata: enhancedResult.metadata,
          charts: enhancedResult.charts,
          chartMetadata: enhancedResult.chartMetadata,
          chartGenerated: enhancedResult.chartGenerated,
          chartType: enhancedResult.chartType,
          originalQuery: userMessage
        };

        // If we have charts but no download info, create a chart-focused message
        if (enhancedResult.charts && enhancedResult.charts.length > 0 && !enhancedResult.downloadInfo) {
          responseMessage.text = `📊 Chart generated successfully! ${enhancedResult.charts.length} visualization(s) created.`;
        }

        console.log('ChatWindow: Adding assistant response:', responseMessage);
        onSendMessage(responseMessage, 'assistant');
      } else {
        // Handle unexpected response
        const errorMessage = {
          id: 'error_' + Date.now(),
          text: `⚠️ Unexpected response format. Please check console for details.`,
          timestamp: new Date(),
          success: false,
          isStructured: false,
          originalQuery: userMessage
        };
        onSendMessage(errorMessage, 'assistant');
      }
    } else if (pendingRequestData?.error) {
      console.error('❌ Error processing request:', pendingRequestData.error);

      // Show error message to user
      const errorMessage = {
        id: 'error_' + Date.now(),
        text: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
        success: false,
        type: 'assistant'
      };

      onSendMessage(errorMessage, 'assistant');
    }

    // Clear pending data
    setPendingRequestData(null);
    setThinkingMessageId(null);
  };

  // Handle thinking skip
  const handleProgressCancel = () => {
    console.log('⏭️ Thinking skipped by user');

    // Immediately complete thinking
    handleThinkingComplete();
  };

  // Test function for thinking message
  const testThinkingMessage = () => {
    console.log('🧪 Testing thinking message...');

    // Add user message
    const userMessage = 'Test thinking message';
    const userMessageObj = {
      id: 'user_test_' + Date.now(),
      text: userMessage,
      timestamp: new Date(),
      success: true,
      isStructured: false,
      originalQuery: userMessage
    };
    onSendMessage(userMessageObj, 'user');

    // Add thinking message
    const thinkingId = 'thinking_test_' + Date.now();
    const thinkingMessage = {
      id: thinkingId,
      text: '',
      timestamp: new Date(),
      success: true,
      isStructured: false,
      showThinking: true,
      originalQuery: userMessage
    };
    console.log('🤔 Sending test thinking message:', thinkingMessage);
    onSendMessage(thinkingMessage, 'assistant');

    // Set up test data and timer
    setThinkingMessageId(thinkingId);
    setPendingRequestData({
      userMessage,
      normalizedMessage: userMessage,
      result: {
        success: true,
        message: 'Test response after thinking',
        downloadInfo: null
      }
    });

    // Set timer
    thinkingTimerRef.current = setTimeout(() => {
      console.log('⏰ Test thinking timer completed');
      handleThinkingComplete();
    }, 5000); // 5 seconds for testing
  };

  const handleEditMessage = (messageId, currentText) => {
    setEditingMessageId(messageId);
    setEditingText(currentText);

    // Use setTimeout to ensure the textarea is rendered before adjusting height
    setTimeout(() => {
      const textarea = document.querySelector('.user-edit-textarea');
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
      }
    }, 0);
  };

  const handleCancelEdit = () => {
    setEditingMessageId(null);
    setEditingText('');
  };

  const handleSaveEdit = () => {
    if (editingText.trim() && editingMessageId) {
      // Find the message and update it
      const updatedConversation = conversation.map(msg =>
        msg.id === editingMessageId
          ? { ...msg, text: editingText.trim() }
          : msg
      );

      // Update the conversation through parent component
      // Note: You might need to add an onUpdateMessage prop to handle this
      console.log('Saving edited message:', editingMessageId, editingText);

      // For now, we'll just reset the editing state
      // In a real implementation, you'd call onUpdateMessage(updatedConversation)
      setEditingMessageId(null);
      setEditingText('');
    }
  };

  const handleSuggestionClick = (suggestionText) => {
    setMessage(suggestionText);
    // Auto-focus the input
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
    console.log('Suggestion clicked:', suggestionText);
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className="chat-container">
        <div className="error-message">
          Browser doesn't support speech recognition. Please use Chrome or Edge.
        </div>
      </div>
    );
  }

  const hasMessages = conversation?.messages?.length > 0;

  return (
    <div className="chat-container">
      {!hasMessages ? (
        <div className="welcome-view">
          <div className="welcome-container">
            <h1 className="brand-title">Report Manager</h1>
            {/* Large centered input box for welcome screen */}
            <div className="welcome-input-container">
              <div className="welcome-input-wrapper-large">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={handleTextareaChange}
                  placeholder={isLoading ? 'Processing your request...' : (isListening ? 'Listening...' : 'Ask me to generate a report...')}
                  disabled={isLoading}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSend();
                    }
                  }}
                  className="chat-input-large"
                  rows="1"
                />

                {/* Tools positioned below the input area */}
                <div className="welcome-tools-bottom">
                  <div className="left-tools">
                    <button className="tool-btn" title="Add">
                      +
                    </button>
                    <button className="tool-btn" title="Tools">
                      <span className="tool-icon">⚙</span>
                      <span>Tools</span>
                    </button>
                    <button className="tool-btn" onClick={addTestDownload} title="Test Download">
                      🧪 Test
                    </button>
                    <button className="tool-btn" onClick={testChartGeneration} title="Test Chart">
                      📊 Chart
                    </button>
                    <button className="tool-btn" onClick={testCompleteWorkflow} title="Test Complete Workflow">
                      🎯 Complete
                    </button>
                    <button className="tool-btn" onClick={testPowerBIIntegration} title="Test Power BI">
                      🔷 Power BI
                    </button>
                    <button className="tool-btn" onClick={testChartJS} title="Test Chart.js">
                      🧪 Test JS
                    </button>
                    <button className="tool-btn" onClick={testThinkingMessage} title="Test Thinking Message">
                      🤔 Think
                    </button>
                  </div>
                  <div className="right-tools">
                    <button
                      className={`tool-btn voice-btn ${isListening ? 'listening' : ''}`}
                      onClick={isListening ? stopListening : startListening}
                      title={isListening ? 'Stop listening' : 'Start voice input'}
                    >
                      🎤
                    </button>
                    <button
                      className="tool-btn send-btn"
                      onClick={handleSend}
                      disabled={!message.trim()}
                      title="Send message"
                    >
                      ↗
                    </button>
                  </div>
                </div>
              </div>

              {/* Welcome screen disclaimer */}
              <div className="welcome-disclaimer">
                <p>ReportDesk can make mistakes. Check important info.</p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="messages-area">
          {console.log('Rendering conversation:', conversation)}
          {console.log('Total messages:', conversation?.messages?.length || 0)}



          {(conversation?.messages || []).map((msg, index) => {
            console.log(`Message ${index}:`, msg);
            console.log(`Message type: ${msg.type}, text: ${msg.text?.substring(0, 50)}...`);
            console.log(`Has downloadInfo:`, !!msg.downloadInfo);
            console.log(`downloadInfo value:`, msg.downloadInfo);
            console.log(`downloadInfo type:`, typeof msg.downloadInfo);
            if (msg.downloadInfo) {
              console.log(`downloadInfo keys:`, Object.keys(msg.downloadInfo));
              console.log(`downloadInfo.downloadUrl:`, msg.downloadInfo.downloadUrl);
            }
            return (
            <div key={msg.id || `${msg.type}-${index}`} className={`message ${msg.type}`}>
              {msg.type === 'user' ? (
                // User message with edit functionality
                <div className="user-message">
                  <div className="user-content">
                    {editingMessageId === msg.id ? (
                      // Edit mode
                      <div className="user-edit-container">
                        <textarea
                          className="user-edit-textarea"
                          value={editingText}
                          onChange={(e) => {
                            setEditingText(e.target.value);
                            // Auto-resize textarea to fit content
                            e.target.style.height = 'auto';
                            e.target.style.height = e.target.scrollHeight + 'px';
                          }}
                          autoFocus
                          onFocus={(e) => {
                            // Set initial height when focused
                            e.target.style.height = 'auto';
                            e.target.style.height = e.target.scrollHeight + 'px';
                          }}
                        />
                        <div className="user-edit-actions">
                          <button
                            className="edit-action-btn cancel-btn"
                            onClick={handleCancelEdit}
                          >
                            Cancel
                          </button>
                          <button
                            className="edit-action-btn save-btn"
                            onClick={handleSaveEdit}
                          >
                            Send
                          </button>
                        </div>
                      </div>
                    ) : (
                      // Display mode
                      <div className="user-display-container">
                        <div className="user-text">{msg.text}</div>
                        <div className="user-message-actions">
                          <button
                            className="user-action-btn"
                            onClick={() => handleCopyMessage(msg.text)}
                            title="Copy message"
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                          </button>
                          <button
                            className="user-action-btn"
                            onClick={() => handleEditMessage(msg.id, msg.text)}
                            title="Edit message"
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                // Simple assistant message display
                <div className="assistant-message">
                  <div className="assistant-content">
                    {msg.isStructured ? (
                      <ResponseRenderer
                        response={msg.text}
                        userMessage={msg.originalQuery || ''}
                      />
                    ) : (msg.showLoading) ? (
                      // Loading animation
                      <LoadingAnimation
                        messages={msg.loadingMessages || [
                          "🔍 Analyzing your request...",
                          "📊 Processing data...",
                          "🎨 Generating charts...",
                          "📋 Creating Excel report...",
                          "✨ Finalizing your report..."
                        ]}
                        duration={4000}
                        onComplete={() => {
                          console.log('⏳ Loading animation completed');
                        }}
                      />
                    ) : (msg.showThinking) ? (
                      // Thinking message (ChatGPT-style)
                      <div>
                        {console.log('🤔 Rendering thinking message for:', msg.id)}
                        <div className="thinking-container">
                          <div className="thinking-content">
                            <div className="thinking-text">
                              Thinking longer for a better answer
                            </div>
                            <button
                              className="skip-button"
                              onClick={handleProgressCancel}
                              aria-label="Skip thinking and show response now"
                            >
                              Skip &gt;
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (msg.showChartTest) ? (
                      // Special test component
                      <div>
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>
                        <ChartTest />
                      </div>
                    ) : (msg.charts && msg.charts.length > 0) ? (
                      // Message with charts (and possibly download)
                      <div>
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>
                        {console.log('🎨 Rendering charts in chat:', msg.charts)}
                        {console.log('🎨 Chart data structure:', JSON.stringify(msg.charts, null, 2))}
                        <ResponseRenderer
                          response={{ charts: msg.charts, chartMetadata: msg.chartMetadata }}
                          userMessage={msg.originalQuery || ''}
                        />
                        {msg.downloadInfo && msg.downloadInfo.downloadUrl && (
                          <div style={{ marginTop: '15px' }}>
                            {console.log('Rendering download info with charts:', msg.downloadInfo)}
                            <DownloadRenderer
                              downloadInfo={msg.downloadInfo}
                              onDownload={(info) => console.log('Downloaded:', info)}
                              chartContext={msg.charts ? `Generated with ${msg.charts.length} chart(s)` : null}
                            />
                          </div>
                        )}
                        {msg.chartLink && (
                          <div style={{ marginTop: '15px' }}>
                            <button
                              className="chart-link-btn"
                              onClick={() => {
                                console.log('🔗 Opening chart in new tab:', msg.chartLink.url);
                                window.open(msg.chartLink.url, '_blank');
                              }}
                              title="Open interactive chart in new browser tab"
                            >
                              🔗 View Interactive Chart
                            </button>
                            <div className="chart-link-info">
                              <span>📊 {msg.chartLink.title}</span>
                              <span>⏰ Expires: {new Date(msg.chartLink.expiresAt).toLocaleDateString()}</span>
                            </div>
                          </div>
                        )}
                        {msg.powerBIReport && (
                          <div style={{ marginTop: '15px' }}>

                            <PowerBIEmbed
                              reportData={msg.powerBIReport}
                              onLoad={(report) => console.log('Power BI report loaded:', report)}
                              onError={(error) => console.error('Power BI error:', error)}
                              height="400px"
                            />
                          </div>
                        )}
                      </div>
                    ) : (msg.downloadInfo && msg.downloadInfo.downloadUrl) ? (
                      <div>
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>
                        {console.log('Rendering download info:', msg.downloadInfo)}
                        <DownloadRenderer
                          downloadInfo={msg.downloadInfo}
                          onDownload={(info) => console.log('Downloaded:', info)}
                        />

                        {msg.powerBIReport && (
                          <div style={{ marginTop: '15px' }}>

                            <PowerBIEmbed
                              reportData={msg.powerBIReport}
                              onLoad={(report) => console.log('Power BI report loaded:', report)}
                              onError={(error) => console.error('Power BI error:', error)}
                              height="400px"
                            />
                          </div>
                        )}

                        {/* Essential Action Icons - Visible Unicode */}
                        <div className="message-actions">
                          <div className="action-buttons">
                            <button
                              className="action-btn"
                              onClick={() => handleCopyMessage(msg.text)}
                              title="Copy message"
                            >
                              📋
                            </button>
                            <button
                              className="action-btn"
                              onClick={() => handleThumbsUp(msg.id)}
                              title="Good response"
                            >
                              👍
                            </button>
                            <button
                              className="action-btn"
                              onClick={() => handleThumbsDown(msg.id)}
                              title="Poor response"
                            >
                              👎
                            </button>
                            <button
                              className="action-btn"
                              onClick={() => handleReadAloud(msg.text)}
                              title="Read aloud"
                            >
                              🔊
                            </button>
                            {msg.downloadInfo && msg.downloadInfo.downloadUrl && (
                              <button
                                className="action-btn"
                                onClick={() => window.open(msg.downloadInfo.downloadUrl, '_blank')}
                                title="Download Excel report"
                              >
                                ⬇️
                              </button>
                            )}
                            <button
                              className="action-btn email-report"
                              onClick={() => handleEmailReport(msg)}
                              title="Email this report"
                            >
                              ✉️
                            </button>

                            <button
                              className="action-btn powerbi-btn"
                              onClick={() => handleGeneratePowerBI(msg)}
                              title="Generate Power BI Report"
                            >
                              🔷
                            </button>
                            <button
                              className="action-btn"
                              onClick={() => window.open(msg.downloadInfo.downloadUrl)}
                              title="Download"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7,10 12,15 17,10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="assistant-text">
                          {typeof msg.text === 'string' ? msg.text : JSON.stringify(msg.text)}
                        </div>

                        {msg.powerBIReport && (
                          <div style={{ marginTop: '15px' }}>

                            <PowerBIEmbed
                              reportData={msg.powerBIReport}
                              onLoad={(report) => console.log('Power BI report loaded:', report)}
                              onError={(error) => console.error('Power BI error:', error)}
                              height="400px"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            );
          })}
          {isLoading && (
            <div className="message assistant">
              <div className="assistant-message">
                <div className="assistant-content">
                  <div className="typing-indicator">
                    <div className="typing-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <span className="typing-text">Processing your request...</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Input area only visible when there are messages */}
      {hasMessages && (
        <div className="input-area">
          <div className="input-container">
            <div className="input-wrapper">
              <div className="left-actions">
                <button className="add-btn" title="Add">
                  +
                </button>
                <button className="tools-btn" title="Tools">
                  <span className="tools-icon">⚙</span>
                  <span>Tools</span>
                </button>
              </div>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Ask anything"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSend();
                  }
                }}
                className="chat-input"
                rows="1"
              />
              <div className="right-actions">
                <button
                  className={`action-btn voice-btn ${isListening ? 'listening' : ''}`}
                  onClick={isListening ? stopListening : startListening}
                  title={isListening ? 'Stop listening' : 'Start voice input'}
                >
                  🎤
                </button>
                <button
                  className="action-btn send-btn"
                  onClick={handleSend}
                  disabled={!message.trim()}
                  title="Send message"
                >
                  ↗
                </button>
              </div>
            </div>
          </div>

          {/* ChatGPT-style disclaimer */}
          <div className="disclaimer">
            <p>ReportDesk can make mistakes. Check important info.</p>
          </div>
        </div>
      )}

      {/* Email Report Modal */}
      <EmailReportModal
        isOpen={emailModalOpen}
        onClose={() => setEmailModalOpen(false)}
        message={selectedMessage}
        userEmail={currentUser.email}
        userName={currentUser.name}
      />


    </div>
  );
}

