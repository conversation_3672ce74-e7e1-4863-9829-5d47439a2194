.chart-viewer-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.chart-viewer-header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.chart-viewer-header h1 {
  margin: 0 0 15px 0;
  color: #2E86AB;
  font-size: 2.5em;
  font-weight: 700;
}

.chart-info {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.chart-info span {
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  border: 1px solid #e9ecef;
}

.chart-type {
  background: #e3f2fd !important;
  color: #1976d2 !important;
  font-weight: 600;
}

.chart-viewer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.chart-display {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.chart-metadata {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-metadata h3 {
  margin: 0 0 20px 0;
  color: #2E86AB;
  font-size: 1.3em;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2E86AB;
}

.metadata-item strong {
  color: #495057;
  font-weight: 600;
}

.metadata-item span {
  color: #6c757d;
  font-weight: 500;
}

.chart-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: 30px 0;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.print-btn {
  background: #28a745;
  color: white;
}

.print-btn:hover {
  background: #218838;
  transform: translateY(-2px);
}

.share-btn {
  background: #17a2b8;
  color: white;
}

.share-btn:hover {
  background: #138496;
  transform: translateY(-2px);
}

.home-btn {
  background: #6c757d;
  color: white;
}

.home-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.chart-summary {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-summary h3 {
  margin: 0 0 20px 0;
  color: #2E86AB;
  font-size: 1.3em;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.summary-item strong {
  color: #495057;
}

.summary-item span {
  color: #6c757d;
  font-weight: 500;
}

.chart-viewer-footer {
  text-align: center;
  margin-top: 40px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.chart-viewer-footer p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* Loading State */
.chart-viewer-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2E86AB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-viewer-loading h2 {
  color: #2E86AB;
  margin: 0 0 10px 0;
}

.chart-viewer-loading p {
  color: #6c757d;
  margin: 0;
}

/* Error State */
.chart-viewer-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.chart-viewer-error h2 {
  color: #dc3545;
  margin: 0 0 15px 0;
  font-size: 2em;
}

.chart-viewer-error p {
  color: #6c757d;
  margin: 0 0 30px 0;
  font-size: 1.1em;
}

.error-suggestions {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #dc3545;
  text-align: left;
  max-width: 500px;
  margin-bottom: 30px;
}

.error-suggestions h3 {
  margin: 0 0 15px 0;
  color: #495057;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.error-suggestions li {
  margin-bottom: 8px;
}

.back-home-btn {
  background: #2E86AB;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.back-home-btn:hover {
  background: #1e5f7a;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-viewer-container {
    padding: 10px;
  }
  
  .chart-viewer-header {
    padding: 20px;
  }
  
  .chart-viewer-header h1 {
    font-size: 2em;
  }
  
  .chart-info {
    gap: 15px;
  }
  
  .chart-display {
    padding: 20px;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 200px;
    justify-content: center;
  }
}

/* Print Styles */
@media print {
  .chart-viewer-container {
    background: white;
    padding: 0;
  }
  
  .chart-actions,
  .chart-viewer-footer {
    display: none;
  }
  
  .chart-display {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
