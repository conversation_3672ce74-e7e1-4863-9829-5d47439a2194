// Email Service for sending reports
class EmailService {
  constructor() {
    // Use import.meta.env for Vite or fallback to localhost
    this.apiBaseUrl = import.meta.env?.VITE_API_BASE_URL || 'http://localhost:8000';
  }

  /**
   * Send report via email to the specified recipient
   * @param {Object} emailData - Email data object
   * @param {string} emailData.to - Recipient email address
   * @param {string} emailData.subject - Email subject
   * @param {string} emailData.reportContent - Main report content
   * @param {string} emailData.customMessage - Optional custom message from user
   * @param {string} emailData.userName - Name of the user sending the email
   * @param {Date} emailData.reportTimestamp - When the report was generated
   * @param {Object} emailData.downloadInfo - Optional download/attachment info
   * @returns {Promise<Object>} Response from email API
   */
  async sendReportEmail(emailData) {
    try {
      console.log('Sending email via API:', emailData);

      // For demo purposes, simulate API call if backend is not available
      if (this.apiBaseUrl.includes('localhost:8000')) {
        console.log('Demo mode: Simulating email send...');

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Generate email content for logging
        const htmlContent = this.generateEmailHTML(emailData);
        const textContent = this.generateEmailText(emailData);

        console.log('Generated HTML Email:', htmlContent);
        console.log('Generated Text Email:', textContent);

        // Return success response
        return {
          success: true,
          message: 'Email sent successfully (Demo Mode)',
          emailId: 'demo_' + Date.now()
        };
      }

      const response = await fetch(`${this.apiBaseUrl}/api/send-report-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers if needed
          // 'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          to: emailData.to,
          subject: emailData.subject,
          htmlContent: this.generateEmailHTML(emailData),
          textContent: this.generateEmailText(emailData),
          attachments: emailData.downloadInfo ? [emailData.downloadInfo] : []
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Email sent successfully:', result);
      return result;

    } catch (error) {
      console.error('Email sending failed:', error);

      // If it's a network error and we're in development, show demo mode
      if (error.message.includes('fetch') || error.message.includes('Network')) {
        console.log('Network error detected, falling back to demo mode...');

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        return {
          success: true,
          message: 'Email sent successfully (Demo Mode - Backend not available)',
          emailId: 'demo_fallback_' + Date.now()
        };
      }

      throw error;
    }
  }

  /**
   * Generate HTML email content
   * @param {Object} emailData - Email data object
   * @returns {string} HTML email content
   */
  generateEmailHTML(emailData) {
    const {
      reportContent,
      customMessage,
      userName,
      reportTimestamp,
      downloadInfo
    } = emailData;

    const formattedDate = new Date(reportTimestamp).toLocaleString();

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report from Report Manager</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 20px; border: 1px solid #e5e7eb; }
          .report-content { background: white; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 15px 0; }
          .custom-message { background: #f0f9ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .footer { background: #374151; color: #d1d5db; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; }
          .attachment { background: #fef3c7; padding: 10px; border-radius: 6px; margin: 10px 0; border-left: 4px solid #f59e0b; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="margin: 0; font-size: 24px;">📊 Report Manager</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">Report generated and shared by ${userName}</p>
          </div>
          
          <div class="content">
            <h2 style="color: #1f2937; margin-top: 0;">Report Details</h2>
            
            ${customMessage ? `
              <div class="custom-message">
                <h3 style="margin-top: 0; color: #1e40af;">Personal Message:</h3>
                <p style="margin-bottom: 0;">${customMessage}</p>
              </div>
            ` : ''}
            
            <div class="report-content">
              <h3 style="margin-top: 0; color: #1f2937;">Report Content:</h3>
              <div style="white-space: pre-wrap;">${reportContent}</div>
            </div>
            
            ${downloadInfo ? `
              <div class="attachment">
                <h3 style="margin-top: 0; color: #92400e;">📎 Attachment Available:</h3>
                <p style="margin-bottom: 0;"><strong>${downloadInfo.filename}</strong></p>
                <p style="margin: 5px 0 0 0; font-size: 14px; color: #78350f;">
                  ${downloadInfo.description || 'Report data file'}
                </p>
              </div>
            ` : ''}
            
            <p style="margin-top: 20px; color: #6b7280; font-size: 14px;">
              <strong>Generated on:</strong> ${formattedDate}
            </p>
          </div>
          
          <div class="footer">
            <p style="margin: 0;">This report was generated by Report Manager</p>
            <p style="margin: 5px 0 0 0;">© ${new Date().getFullYear()} Report Manager. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate plain text email content
   * @param {Object} emailData - Email data object
   * @returns {string} Plain text email content
   */
  generateEmailText(emailData) {
    const {
      reportContent,
      customMessage,
      userName,
      reportTimestamp,
      downloadInfo
    } = emailData;

    const formattedDate = new Date(reportTimestamp).toLocaleString();

    let textContent = `REPORT FROM REPORT MANAGER\n`;
    textContent += `Generated and shared by: ${userName}\n`;
    textContent += `Generated on: ${formattedDate}\n\n`;

    if (customMessage) {
      textContent += `PERSONAL MESSAGE:\n${customMessage}\n\n`;
    }

    textContent += `REPORT CONTENT:\n${reportContent}\n\n`;

    if (downloadInfo) {
      textContent += `ATTACHMENT:\n📎 ${downloadInfo.filename}\n`;
      if (downloadInfo.description) {
        textContent += `${downloadInfo.description}\n`;
      }
      textContent += `\n`;
    }

    textContent += `---\nThis report was generated by Report Manager\n`;
    textContent += `© ${new Date().getFullYear()} Report Manager. All rights reserved.`;

    return textContent;
  }

  /**
   * Validate email address format
   * @param {string} email - Email address to validate
   * @returns {boolean} True if email is valid
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// Export singleton instance
const emailService = new EmailService();
export default emailService;
