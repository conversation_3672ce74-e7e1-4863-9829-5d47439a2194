.bar-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  border: 1px solid #e1e5e9;
}

.chart-wrapper {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
}

.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
  color: #6c757d;
  font-size: 16px;
  margin: 20px 0;
}

.chart-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  width: 100%;
  max-width: 600px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
}

.summary-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  font-size: 16px;
  font-weight: bold;
  color: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
  .bar-chart-container {
    padding: 15px;
    margin: 15px 0;
  }
  
  .chart-wrapper {
    width: 100% !important;
    height: 300px !important;
  }
  
  .chart-summary {
    flex-direction: column;
    gap: 10px;
  }
  
  .summary-item {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
    width: 100%;
  }
}

/* Animation for chart container */
.bar-chart-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
