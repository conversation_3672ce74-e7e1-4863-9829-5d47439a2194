#!/usr/bin/env python3
"""
Test Multi-Database Functionality
"""

import requests
import json

def test_database_connections():
    """Test both SQLite and Excel database connections"""
    
    print("🧪 Testing Multi-Database Functionality")
    print("=" * 60)
    
    # Test 1: Tax Code Query (SQLite Database)
    print("\n1️⃣ Testing SQLite Database (Tax Codes)")
    print("-" * 40)
    
    try:
        response = requests.post(
            "http://localhost:8007/query/excel-clean",
            json={"text": "I need all tax code from einvoicing"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                sheet = data['sheets'][0]
                print(f"✅ SQLite Query Success!")
                print(f"   Sheet: {sheet.get('name')}")
                print(f"   Rows: {len(sheet.get('rows', []))}")
                print(f"   Columns: {len(sheet.get('headers', []))}")
                print(f"   Datasource: {data['metadata'].get('datasource')}")
                print(f"   SQL: {data['metadata'].get('sql_query')}")
            else:
                print(f"❌ SQLite Query Failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Employee Query (Excel Database)
    print("\n2️⃣ Testing Excel Database (Employees)")
    print("-" * 40)
    
    try:
        response = requests.post(
            "http://localhost:8007/query/excel-clean",
            json={"text": "I need all employees"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                sheet = data['sheets'][0]
                print(f"✅ Excel Query Success!")
                print(f"   Sheet: {sheet.get('name')}")
                print(f"   Rows: {len(sheet.get('rows', []))}")
                print(f"   Columns: {len(sheet.get('headers', []))}")
                print(f"   Datasource: {data['metadata'].get('datasource')}")
                print(f"   Sample Employee: {sheet.get('rows', [[]])[0][:3] if sheet.get('rows') else 'No data'}")
            else:
                print(f"❌ Excel Query Failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Different Employee Queries
    print("\n3️⃣ Testing Different Employee Queries")
    print("-" * 40)
    
    employee_queries = [
        "show me employees in engineering department",
        "list all staff members",
        "get employee salary information",
        "show me HR department employees"
    ]
    
    for query in employee_queries:
        print(f"\n🔍 Query: {query}")
        try:
            response = requests.post(
                "http://localhost:8007/query/excel-clean",
                json={"text": query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ Success - Datasource: {data['metadata'].get('datasource')}")
                    print(f"   📊 Rows: {len(data['sheets'][0].get('rows', []))}")
                else:
                    print(f"   ❌ Failed: {data.get('error')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Multi-Database Test Complete!")

if __name__ == "__main__":
    test_database_connections()
