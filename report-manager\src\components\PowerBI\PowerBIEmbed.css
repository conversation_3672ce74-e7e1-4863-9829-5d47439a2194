/* Power BI Embed Component Styles */

.powerbi-embed-container {
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  background: #ffffff;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.powerbi-embed-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.powerbi-embed-container.expanded {
  position: fixed;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  margin: 0;
  border-radius: 8px;
}

/* Header */
.powerbi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  color: white;
  border-bottom: 1px solid #e1e5e9;
}

.powerbi-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.powerbi-icon {
  font-size: 18px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.title-text {
  font-weight: 600;
  font-size: 14px;
  color: white;
}

.data-points {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.powerbi-actions {
  display: flex;
  gap: 8px;
}

.powerbi-actions .action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.powerbi-actions .action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.powerbi-actions .action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Content */
.powerbi-content {
  position: relative;
  width: 100%;
  min-height: 300px;
  background: #f8f9fa;
}

.powerbi-iframe-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Loading State */
.powerbi-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  background: #f8f9fa;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e1e5e9;
  border-top: 3px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #323130;
  margin-bottom: 4px;
}

.loading-subtext {
  font-size: 14px;
  color: #605e5c;
}

/* Error State */
.powerbi-error,
.powerbi-embed-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  background: #f8f9fa;
  padding: 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: #d13438;
  text-align: center;
  margin-bottom: 16px;
}

.retry-btn {
  background: #0078d4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: #106ebe;
}

/* Mock Power BI Report */
.mock-powerbi-report {
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
}

.mock-report-header {
  padding: 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #faf9f8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mock-report-title {
  font-size: 18px;
  font-weight: 600;
  color: #323130;
}

.mock-report-filters {
  display: flex;
  gap: 8px;
}

.filter-chip {
  background: #e1e5e9;
  color: #323130;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.mock-report-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mock-chart-container {
  flex: 1;
}

.mock-chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #323130;
  margin-bottom: 16px;
}

.mock-chart {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mock-chart-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bar-label {
  min-width: 100px;
  font-size: 12px;
  color: #605e5c;
  font-weight: 500;
}

.bar-fill {
  flex: 1;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  position: relative;
  min-width: 60px;
}

.bar-value {
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.mock-kpi-section {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.kpi-card {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  flex: 1;
  min-width: 120px;
  text-align: center;
}

.kpi-title {
  font-size: 12px;
  color: #605e5c;
  font-weight: 500;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  font-size: 20px;
  font-weight: 700;
  color: #323130;
}

.kpi-value.status-active {
  color: #107c10;
}

.mock-report-footer {
  padding: 12px 16px;
  border-top: 1px solid #e1e5e9;
  background: #faf9f8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #605e5c;
}

.powerbi-branding {
  display: flex;
  align-items: center;
  gap: 6px;
}

.powerbi-logo {
  font-size: 14px;
}

/* Footer */
.powerbi-footer {
  padding: 8px 16px;
  background: #faf9f8;
  border-top: 1px solid #e1e5e9;
}

.report-metadata {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #605e5c;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .powerbi-embed-container.expanded {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
  }
  
  .powerbi-header {
    padding: 8px 12px;
  }
  
  .title-text {
    font-size: 13px;
  }
  
  .mock-kpi-section {
    flex-direction: column;
  }
  
  .kpi-card {
    min-width: auto;
  }
  
  .mock-report-footer {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
