import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import './BarChart.css';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const BarChart = ({ 
  data, 
  title = "Bar Chart", 
  width = 600, 
  height = 400,
  showLegend = true,
  showTooltips = true,
  horizontal = false,
  colors = [
    '#36A2EB', '#FF6384', '#FFCE56', '#4BC0C0', 
    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
  ]
}) => {
  if (!data || !data.labels || !data.values) {
    return (
      <div className="chart-error">
        <p>📊 No data available for chart</p>
      </div>
    );
  }

  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: data.datasetLabel || 'Values',
        data: data.values,
        backgroundColor: colors.slice(0, data.labels.length),
        borderColor: colors.slice(0, data.labels.length).map(color => color + '80'),
        borderWidth: 2,
        borderRadius: 4,
        borderSkipped: false,
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: horizontal ? 'y' : 'x',
    plugins: {
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      legend: {
        display: showLegend,
        position: 'top',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        enabled: showTooltips,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#fff',
        borderWidth: 1,
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y || context.parsed.x;
            return `${label}: ${value.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1
        },
        ticks: {
          font: {
            size: 11
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart'
    }
  };

  return (
    <div className="bar-chart-container">
      <div className="chart-wrapper" style={{ width, height }}>
        <Bar data={chartData} options={options} />
      </div>
      {data.summary && (
        <div className="chart-summary">
          <div className="summary-item">
            <span className="summary-label">Total:</span>
            <span className="summary-value">
              {data.values.reduce((a, b) => a + b, 0).toLocaleString()}
            </span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Average:</span>
            <span className="summary-value">
              {(data.values.reduce((a, b) => a + b, 0) / data.values.length).toLocaleString()}
            </span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Max:</span>
            <span className="summary-value">
              {Math.max(...data.values).toLocaleString()}
            </span>
          </div>
          {data.summary.map((item, index) => (
            <div key={index} className="summary-item">
              <span className="summary-label">{item.label}:</span>
              <span className="summary-value">{item.value}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BarChart;
