"""
Result Classes Module

This module contains result classes for various operations.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class QueryResult:
    """Result from a query operation"""
    success: bool
    data: Optional[Any] = None
    query_type: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class OrchestrationResult:
    """Final result from orchestration"""
    success: bool
    result: Any
    agents_used: List[str]
    execution_time: float
    context: Optional[Any] = None
    flow_id: Optional[str] = None
    error: Optional[str] = None
