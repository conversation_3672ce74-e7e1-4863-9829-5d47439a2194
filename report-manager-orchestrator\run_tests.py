#!/usr/bin/env python3
"""
Test Runner for Enterprise Multi-Database Query System

This script provides an easy way to run different categories of tests.
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, description):
    """Run a command and handle the output"""
    print(f"\n🧪 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ FAILED")
            if result.stderr:
                print(result.stderr)
            if result.stdout:
                print(result.stdout)
                
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Run tests for Enterprise Multi-Database Query System")
    parser.add_argument("--category", "-c", 
                       choices=["all", "unit", "integration", "functional", "quick"],
                       default="quick",
                       help="Test category to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", action="store_true", help="Run with coverage")
    
    args = parser.parse_args()
    
    print("🏗️ Enterprise Multi-Database Query System - Test Runner")
    print("=" * 60)
    
    # Check if tests directory exists
    tests_dir = Path("tests")
    if not tests_dir.exists():
        print("❌ Tests directory not found!")
        return 1
    
    # Build base command
    base_cmd = "python"
    verbose_flag = " -v" if args.verbose else ""
    coverage_flag = " --cov=src" if args.coverage else ""
    
    success = True
    
    if args.category == "all":
        print("🎯 Running ALL tests...")
        
        # Run unit tests
        cmd = f"{base_cmd} -m pytest tests/unit/{verbose_flag}{coverage_flag}"
        success &= run_command(cmd, "Unit Tests")
        
        # Run integration tests
        cmd = f"{base_cmd} -m pytest tests/integration/{verbose_flag}"
        success &= run_command(cmd, "Integration Tests")
        
        # Run functional tests
        cmd = f"{base_cmd} -m pytest tests/functional/{verbose_flag}"
        success &= run_command(cmd, "Functional Tests")
        
    elif args.category == "unit":
        cmd = f"{base_cmd} -m pytest tests/unit/{verbose_flag}{coverage_flag}"
        success = run_command(cmd, "Unit Tests")
        
    elif args.category == "integration":
        cmd = f"{base_cmd} -m pytest tests/integration/{verbose_flag}"
        success = run_command(cmd, "Integration Tests")
        
    elif args.category == "functional":
        cmd = f"{base_cmd} -m pytest tests/functional/{verbose_flag}"
        success = run_command(cmd, "Functional Tests")
        
    elif args.category == "quick":
        print("🚀 Running Quick Tests (Key Functionality)...")
        
        # Run key functional tests
        key_tests = [
            "tests/functional/test_multi_database.py",
            "tests/functional/test_name_filtering.py",
            "tests/functional/test_employee_filtering.py"
        ]
        
        for test_file in key_tests:
            if Path(test_file).exists():
                cmd = f"{base_cmd} {test_file}"
                test_name = Path(test_file).stem.replace("test_", "").replace("_", " ").title()
                success &= run_command(cmd, f"Quick Test: {test_name}")
            else:
                print(f"⚠️  Test file not found: {test_file}")
    
    # Summary
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Your Enterprise Multi-Database Query System is working perfectly!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n🔍 Check the output above for details.")
    
    print("\n📚 For more testing options:")
    print("   python run_tests.py --help")
    print("   python run_tests.py -c all -v --coverage")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
