/* Gmail-Style Professional Email <PERSON> */
.email-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.email-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  max-width: 540px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.2s ease-out;
  border: 1px solid #dadce0;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Gmail-Style Header */
.email-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #dadce0;
  background: #f8f9fa;
}

.email-modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #3c4043;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #5f6368;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.15s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: #f1f3f4;
  color: #202124;
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Gmail-Style Content */
.email-modal-content {
  padding: 0;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.email-info {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.email-field {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  font-size: 14px;
}

.email-field label {
  font-weight: 500;
  color: #5f6368;
  min-width: 50px;
  margin-right: 12px;
  font-size: 13px;
}

.email-address {
  color: #1a73e8;
  font-weight: 400;
}

.report-preview {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.report-preview label {
  display: block;
  font-weight: 500;
  color: #3c4043;
  margin-bottom: 8px;
  font-size: 13px;
}

.report-content {
  background: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 16px;
  font-size: 13px;
  line-height: 1.5;
  color: #3c4043;
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Google Sans', Roboto, Arial, sans-serif;
}

.report-text {
  margin-bottom: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.attachment-section {
  border-top: 1px solid #e8eaed;
  padding-top: 12px;
  margin-top: 12px;
}

.attachment-info {
  padding: 8px 12px;
  background: #e8f0fe;
  border: 1px solid #d2e3fc;
  border-radius: 4px;
  font-size: 12px;
  color: #1a73e8;
  margin-bottom: 6px;
}

.attachment-details {
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: #5f6368;
}

.attachment-details span {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
}

.custom-message-section {
  padding: 16px 24px;
}

.custom-message-section label {
  display: block;
  font-weight: 500;
  color: #3c4043;
  margin-bottom: 8px;
  font-size: 13px;
}

.custom-message-section textarea {
  width: 100%;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
  min-height: 80px;
  font-family: 'Google Sans', Roboto, Arial, sans-serif;
  box-sizing: border-box;
}

.custom-message-section textarea:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.custom-message-section textarea:disabled {
  background-color: #f8f9fa;
  color: #5f6368;
}

.char-count {
  text-align: right;
  font-size: 11px;
  color: #5f6368;
  margin-top: 4px;
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 20px;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.success-message h4 {
  margin: 0 0 8px 0;
  color: #059669;
  font-size: 18px;
  font-weight: 600;
}

.success-message p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* Error Message */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px;
  color: #dc2626;
  font-size: 14px;
  margin-bottom: 16px;
}

/* Gmail-Style Footer */
.email-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 24px;
  border-top: 1px solid #dadce0;
  background: #f8f9fa;
}

.cancel-btn, .send-btn {
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid;
  display: flex;
  align-items: center;
  gap: 6px;
  font-family: 'Google Sans', Roboto, Arial, sans-serif;
}

.cancel-btn {
  background: #ffffff;
  color: #3c4043;
  border-color: #dadce0;
}

.cancel-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #bdc1c6;
}

.send-btn {
  background: #1a73e8;
  color: #ffffff;
  border-color: #1a73e8;
  min-width: 80px;
}

.send-btn:hover:not(:disabled) {
  background: #1557b0;
  border-color: #1557b0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.send-btn:disabled {
  background: #f1f3f4;
  color: #9aa0a6;
  border-color: #f1f3f4;
  cursor: not-allowed;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .email-modal-overlay {
    padding: 10px;
  }
  
  .email-modal {
    max-width: 100%;
  }
  
  .email-modal-header,
  .email-modal-content,
  .email-modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .email-field {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .email-field label {
    margin-bottom: 4px;
    margin-right: 0;
  }
}
