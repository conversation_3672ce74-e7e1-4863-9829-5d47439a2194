#!/usr/bin/env python3
"""
Test Fixed Employee Filtering
"""

import requests
import json

def test_fixed_filtering():
    """Test the fixed employee filtering functionality"""
    
    print("🧪 Testing Fixed Employee Filtering")
    print("=" * 60)
    
    # Test the specific query that was failing
    query = "I need all employee for which year of service is greater than 3"
    
    print(f"🔍 Testing Query: {query}")
    print("-" * 60)
    
    try:
        response = requests.post(
            "http://localhost:8009/query/excel-clean",
            json={"text": query},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                sheet = data['sheets'][0]
                metadata = data['metadata']
                
                print(f"✅ Query Success!")
                print(f"📊 Rows returned: {len(sheet.get('rows', []))}")
                print(f"🗄️ Datasource: {metadata.get('datasource')}")
                print(f"🔍 Generated SQL: {metadata.get('sql_query')}")
                print(f"🎯 Confidence: {metadata.get('confidence')}")
                
                # Analyze the results
                rows = sheet.get('rows', [])
                headers = sheet.get('headers', [])
                
                if rows:
                    print(f"\n📄 Sample Results:")
                    
                    # Find column indices
                    name_idx = headers.index('FullName') if 'FullName' in headers else None
                    dept_idx = headers.index('Department') if 'Department' in headers else None
                    years_idx = headers.index('YearsOfService') if 'YearsOfService' in headers else None
                    salary_idx = headers.index('Salary') if 'Salary' in headers else None
                    
                    # Show first 5 results
                    for i, row in enumerate(rows[:5]):
                        sample_info = []
                        if name_idx is not None:
                            sample_info.append(f"Name: {row[name_idx]}")
                        if dept_idx is not None:
                            sample_info.append(f"Dept: {row[dept_idx]}")
                        if years_idx is not None:
                            sample_info.append(f"Years: {row[years_idx]}")
                        if salary_idx is not None:
                            sample_info.append(f"Salary: ${row[salary_idx]}")
                        
                        print(f"   {i+1}. {', '.join(sample_info)}")
                    
                    # Verify filtering
                    if years_idx is not None:
                        years_over_3 = [row for row in rows if row[years_idx] > 3]
                        years_under_3 = [row for row in rows if row[years_idx] <= 3]
                        
                        print(f"\n🎯 Filtering Verification:")
                        print(f"   Total rows returned: {len(rows)}")
                        print(f"   Employees with >3 years: {len(years_over_3)}")
                        print(f"   Employees with ≤3 years: {len(years_under_3)}")
                        
                        if len(years_under_3) == 0:
                            print(f"   ✅ PERFECT! All employees have >3 years of service")
                        else:
                            print(f"   ❌ ISSUE: {len(years_under_3)} employees have ≤3 years")
                            
                        if len(rows) < 85:  # Total employees
                            print(f"   ✅ FILTERING WORKED! ({len(rows)} < 85 total employees)")
                        else:
                            print(f"   ❌ NO FILTERING: Returned all {len(rows)} employees")
                    
                else:
                    print("   ❌ No data returned")
                    
            else:
                print(f"❌ Query Failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Fixed Filtering Test Complete!")

if __name__ == "__main__":
    test_fixed_filtering()
