# Report Manager Orchestrator

An AI-powered orchestrator that processes user text, creates context with LLM, and coordinates agent calls to fulfill user requests.

## Architecture

The system consists of several key components:

### Core Components

1. **Orchestrator** (`orchestrator.py`) - Main coordinator that processes user input
2. **Flow Controller** (`flow.py`) - Manages execution flow and multi-step workflows  
3. **Router Agent** (`agents/router_agent.py`) - Determines which agents to call based on context
4. **Query Agent** (`agents/query_agent.py`) - Handles data retrieval and basic analysis
5. **IO Handler** (`utils/io.py`) - Manages input/output formatting and display

### How It Works

1. **User Input Processing**: User provides text input through CLI or API
2. **Context Creation**: LLM analyzes the input to understand intent, extract entities, and determine task type
3. **Agent Routing**: Router agent selects appropriate specialized agents based on context
4. **Execution**: Selected agents process the request in coordinated workflow
5. **Result Formatting**: Results are formatted and presented to the user

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd report-manager-orchestrator
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

## Usage

### API Server (Recommended)

#### Start the API server:
```bash
# Simple startup
python start_api.py

# Or using CLI
python main.py serve

# Custom host/port
python main.py serve --host 0.0.0.0 --port 8080
```

#### Access the API:
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### Command Line Interface

#### Process a single query:
```bash
python main.py query "Show me all completed reports"
```

#### Interactive mode:
```bash
python main.py interactive
```

#### Check system status:
```bash
python main.py status
```

#### Test LLM connection:
```bash
python main.py test-connection
```

#### Test API:
```bash
python main.py api-test
```

### Query Examples

- **Data Retrieval**: "Show me all reports"
- **Filtering**: "Get completed sales reports"  
- **Analysis**: "Analyze the trend in customer metrics"
- **Search**: "Find reports about marketing"
- **Aggregation**: "Count reports by status"

## Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `OPENAI_MODEL`: Model to use (default: gpt-4)
- `OPENAI_TEMPERATURE`: Temperature setting (default: 0.1)

### Agent Configuration

Agents are configured in `agents/router_agent.py` with their capabilities and priorities:

- **Query Agent**: Data retrieval, filtering, search, basic analysis
- **Report Agent**: Report generation, formatting, visualization  
- **Workflow Agent**: Process automation, task scheduling
- **Analysis Agent**: Advanced statistical analysis, insights

## Development

### Adding New Agents

1. Create agent class in `agents/` directory
2. Implement required methods (`process`, etc.)
3. Register agent in `RouterAgent.agent_capabilities`
4. Update orchestrator to handle the new agent

### Extending Functionality

- Add new task types in `TaskType` enum
- Implement new query handlers in `QueryAgent`
- Add new routing rules in `RouterAgent`
- Extend context creation logic in orchestrator

## Architecture Diagram

```
User Input
    ↓
Orchestrator (LLM Context Creation)
    ↓
Router Agent (Agent Selection)
    ↓
Flow Controller (Execution Management)
    ↓
Specialized Agents (Query/Report/Analysis/Workflow)
    ↓
Result Formatting & Output
```

## API Reference

### Main Classes

#### `ReportManagerOrchestrator`
- `process_user_input(text)`: Main entry point for processing
- `create_context(text)`: Create context from user input using LLM
- `route_to_agents(context)`: Determine which agents to use
- `execute_agent_workflow(context, agents)`: Execute agent workflow

#### `FlowController`  
- `execute_simple_flow(text)`: Execute single-step flow
- `execute_complex_flow(text, flow_id)`: Execute multi-step flow
- `get_flow_status(flow_id)`: Get status of running flow

#### `RouterAgent`
- `route(context, suggested_agents)`: Route to appropriate agents
- `list_available_agents()`: Get list of available agents
- `get_agent_info(agent_name)`: Get agent capabilities

#### `QueryAgent`
- `process(context)`: Process query based on context
- `handle_data_retrieval(context)`: Handle data retrieval queries
- `handle_filtering(context)`: Handle filtering queries
- `handle_search(context)`: Handle search queries
- `handle_basic_analysis(context)`: Handle analysis queries

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]
