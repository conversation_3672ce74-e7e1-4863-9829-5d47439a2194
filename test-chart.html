<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background: #e9ecef;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .chart-preview {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #007bff;
            border-radius: 8px;
            text-align: center;
            background: #f8f9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Chart Functionality Test</h1>
        
        <div class="test-section">
            <h3>🎯 Test Unified Chart Streaming</h3>
            <p>Test the unified streaming API that includes chart data in the stream response.</p>

            <button class="test-button" onclick="testStreamingWithChart()">
                📊 Test Streaming Chart Query
            </button>

            <button class="test-button" onclick="testStreamingWithoutChart()">
                📋 Test Regular Streaming Query
            </button>

            <button class="test-button" onclick="testCompleteWorkflow()">
                🎯 Test Complete Chart + Excel Workflow
            </button>

            <button class="test-button" onclick="testHealthCheck()">
                ❤️ Test Health Check
            </button>

            <div id="api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎨 Chart Data Preview</h3>
            <p>Preview the chart data structure that will be sent to the frontend components.</p>
            
            <div class="chart-preview">
                <h4>📊 Sample Donut Chart Data Structure</h4>
                <pre id="chart-data-preview"></pre>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Unified Chart Streaming Integration</h3>
            <p>How the unified chart streaming works in your React frontend:</p>

            <div class="result">
<strong>🎯 NEW UNIFIED APPROACH:</strong>
Charts are now automatically included in the streaming response when detected!

<strong>1. Import Chart Components:</strong>
import { ChartDisplay } from '../components/Charts';
import streamingApi from '../services/streamingApi';

<strong>2. Make Streaming Request (Same as Before):</strong>
const result = await streamingApi.generateReport(userMessage, 'excel');

<strong>3. Charts Are Automatically Included:</strong>
if (result.charts && result.charts.length > 0) {
  // Charts are already in the result!
  &lt;ChartDisplay charts={result.charts} /&gt;
}

<strong>4. No Separate Chart API Calls Needed:</strong>
// The streaming API automatically detects chart keywords and includes chart data
// Keywords: chart, graph, visualization, donut, pie, bar, plot

<strong>5. Streaming Response Structure:</strong>
{
  success: true,
  excelData: {...},
  charts: [{...}],  // ← Charts included here!
  chartGenerated: true,
  chartType: "donut"
}

<strong>6. Chart Detection Keywords:</strong>
- chart, graph, visualization, donut, pie, bar, plot, visual

<strong>7. Sample Queries (Same API Endpoint):</strong>
- "Give me donut chart for all employee salary"
- "Show me salary distribution by department"
- "Create a bar chart of employee data"
- "Show me all employees" (no chart - regular data)

<strong>8. Benefits:</strong>
✅ Single API endpoint for all queries
✅ Automatic chart detection
✅ Charts included in streaming response
✅ No separate chart API calls needed
✅ Backward compatible with existing code
            </div>
        </div>
    </div>

    <script>
        // Sample chart data for preview
        const sampleChartData = {
            type: "donut",
            title: "Employee Salary Distribution by Department",
            data: {
                labels: ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations"],
                values: [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
                summary: [
                    { label: "Departments", value: "6" },
                    { label: "Total Employees", value: "150" },
                    { label: "Avg Salary", value: "$85,000" }
                ]
            },
            options: {
                width: 500,
                height: 400
            }
        };

        // Display sample chart data
        document.getElementById('chart-data-preview').textContent = JSON.stringify(sampleChartData, null, 2);

        async function testStreamingWithChart() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = 'Testing streaming API with chart query...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('http://localhost:8000/query/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: "Give me donut chart for all employee salary",
                        output_format: "excel",
                        chunk_size: 50
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunks = [];
                let chartFound = false;

                resultDiv.textContent = 'Streaming in progress...\n\n';
                resultDiv.className = 'result';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                chunks.push(data);

                                if (data.type === 'chart') {
                                    chartFound = true;
                                    resultDiv.textContent += `📊 CHART DATA RECEIVED!\nChart Type: ${data.chart.type}\nTitle: ${data.chart.title}\n\n`;
                                }

                                if (data.type === 'complete') {
                                    const summary = `✅ STREAMING COMPLETE!\n\nChart Generated: ${data.chart_generated || false}\nChart Type: ${data.chart_type || 'none'}\nTotal Chunks: ${chunks.length}\nChart Found in Stream: ${chartFound}\n\n`;

                                    if (chartFound) {
                                        resultDiv.textContent = summary + 'SUCCESS: Chart data was included in streaming response!\n\n' + JSON.stringify(chunks.filter(c => c.type === 'chart'), null, 2);
                                        resultDiv.className = 'result success';
                                    } else {
                                        resultDiv.textContent = summary + 'WARNING: No chart data found in streaming response.\n\nFull response:\n' + JSON.stringify(chunks, null, 2);
                                        resultDiv.className = 'result error';
                                    }
                                    return;
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse chunk:', line);
                            }
                        }
                    }
                }
            } catch (error) {
                resultDiv.textContent = '❌ NETWORK ERROR: ' + error.message + '\n\nMake sure the backend server is running on http://localhost:8000';
                resultDiv.className = 'result error';
            }
        }

        async function testStreamingWithoutChart() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = 'Testing streaming API with regular query...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('http://localhost:8000/query/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: "Show me all employees",
                        output_format: "excel",
                        chunk_size: 50
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunks = [];

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                chunks.push(data);

                                if (data.type === 'complete') {
                                    const summary = `✅ REGULAR STREAMING COMPLETE!\n\nChart Generated: ${data.chart_generated || false}\nTotal Chunks: ${chunks.length}\n\n`;
                                    resultDiv.textContent = summary + 'SUCCESS: Regular query processed without chart data.\n\nMetadata:\n' + JSON.stringify(chunks.filter(c => c.type === 'metadata'), null, 2);
                                    resultDiv.className = 'result success';
                                    return;
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse chunk:', line);
                            }
                        }
                    }
                }
            } catch (error) {
                resultDiv.textContent = '❌ NETWORK ERROR: ' + error.message + '\n\nMake sure the backend server is running on http://localhost:8000';
                resultDiv.className = 'result error';
            }
        }

        async function testCompleteWorkflow() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = 'Testing complete chart + Excel workflow...\n\n';
            resultDiv.className = 'result';

            try {
                // Test the complete workflow: Chart query -> Streaming response -> Chart + Excel data
                const response = await fetch('http://localhost:8000/query/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: "Give me donut chart for all employee salary distribution",
                        output_format: "excel",
                        chunk_size: 50
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunks = [];
                let chartFound = false;
                let excelDataFound = false;
                let chartData = null;
                let excelSheets = {};

                resultDiv.textContent += 'Streaming response analysis:\n';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                chunks.push(data);

                                if (data.type === 'chart') {
                                    chartFound = true;
                                    chartData = data.chart;
                                    resultDiv.textContent += `✅ Chart data received: ${data.chart.type} chart with ${data.chart.data.labels.length} data points\n`;
                                }

                                if (data.type === 'data' && data.chunk) {
                                    excelDataFound = true;
                                    const sheetName = data.chunk.sheet_name || 'Data';
                                    if (!excelSheets[sheetName]) {
                                        excelSheets[sheetName] = { headers: [], rows: [] };
                                    }
                                    if (data.chunk.type === 'headers') {
                                        excelSheets[sheetName].headers = data.chunk.data;
                                    } else if (data.chunk.type === 'rows') {
                                        excelSheets[sheetName].rows.push(...data.chunk.data);
                                    }
                                    resultDiv.textContent += `📊 Excel data chunk received for sheet: ${sheetName}\n`;
                                }

                                if (data.type === 'complete') {
                                    resultDiv.textContent += `\n🎯 WORKFLOW ANALYSIS:\n`;
                                    resultDiv.textContent += `Chart Generated: ${data.chart_generated || false}\n`;
                                    resultDiv.textContent += `Chart Type: ${data.chart_type || 'none'}\n`;
                                    resultDiv.textContent += `Total Chunks: ${chunks.length}\n`;
                                    resultDiv.textContent += `Chart Found: ${chartFound}\n`;
                                    resultDiv.textContent += `Excel Data Found: ${excelDataFound}\n`;
                                    resultDiv.textContent += `Excel Sheets: ${Object.keys(excelSheets).join(', ')}\n\n`;

                                    // Verify complete workflow
                                    if (chartFound && excelDataFound) {
                                        resultDiv.textContent += `✅ SUCCESS: Complete workflow working!\n`;
                                        resultDiv.textContent += `📊 Chart: ${chartData.title}\n`;
                                        resultDiv.textContent += `📋 Excel: ${Object.keys(excelSheets).length} sheet(s) with data\n\n`;

                                        resultDiv.textContent += `FRONTEND INTEGRATION:\n`;
                                        resultDiv.textContent += `• Charts will display in chat interface\n`;
                                        resultDiv.textContent += `• Excel download will be available\n`;
                                        resultDiv.textContent += `• Both work from single API call\n\n`;

                                        resultDiv.textContent += `Chart Data Preview:\n${JSON.stringify(chartData, null, 2)}`;
                                        resultDiv.className = 'result success';
                                    } else if (chartFound && !excelDataFound) {
                                        resultDiv.textContent += `⚠️ PARTIAL: Chart found but no Excel data\n`;
                                        resultDiv.textContent += `This means charts work but Excel generation may have issues.\n`;
                                        resultDiv.className = 'result error';
                                    } else if (!chartFound && excelDataFound) {
                                        resultDiv.textContent += `⚠️ PARTIAL: Excel data found but no chart\n`;
                                        resultDiv.textContent += `This means chart detection may not be working.\n`;
                                        resultDiv.className = 'result error';
                                    } else {
                                        resultDiv.textContent += `❌ FAILURE: Neither chart nor Excel data found\n`;
                                        resultDiv.className = 'result error';
                                    }
                                    return;
                                }
                            } catch (parseError) {
                                console.warn('Failed to parse chunk:', line);
                            }
                        }
                    }
                }
            } catch (error) {
                resultDiv.textContent = '❌ NETWORK ERROR: ' + error.message + '\n\nMake sure the backend server is running on http://localhost:8000';
                resultDiv.className = 'result error';
            }
        }

        async function testHealthCheck() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = 'Testing health check endpoint...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = '✅ SUCCESS: Backend server is running!\n\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ ERROR: Health check failed\n\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '❌ NETWORK ERROR: ' + error.message + '\n\nBackend server is not running. Please start it with:\npython -m src.report_manager.api.server';
                resultDiv.className = 'result error';
            }
        }

        // Auto-test health check on page load
        window.addEventListener('load', () => {
            setTimeout(testHealthCheck, 1000);
        });
    </script>
</body>
</html>
