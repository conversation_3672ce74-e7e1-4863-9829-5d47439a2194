/**
 * Power BI Service for generating and embedding Power BI reports
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

class PowerBIService {
  constructor() {
    this.powerBIConfig = {
      // These would typically come from environment variables
      clientId: import.meta.env.VITE_POWERBI_CLIENT_ID || 'demo-client-id',
      tenantId: import.meta.env.VITE_POWERBI_TENANT_ID || 'demo-tenant-id',
      workspaceId: import.meta.env.VITE_POWERBI_WORKSPACE_ID || 'demo-workspace-id',
      embedUrl: 'https://app.powerbi.com/reportEmbed',
      tokenUrl: `${API_BASE_URL}/powerbi/token`,
      reportUrl: `${API_BASE_URL}/powerbi/generate`
    };
  }

  /**
   * Generate Power BI report from chart data
   * @param {Object} chartData - Chart data object
   * @param {Object} metadata - Report metadata
   * @param {string} reportTitle - Title for the report
   * @returns {Promise<Object>} Power BI report info
   */
  async generateReport(chartData, metadata = {}, reportTitle = 'Data Report') {
    try {
      console.log('🔷 Generating Power BI report...', { chartData, metadata, reportTitle });

      // For now, we'll create a mock Power BI report
      // In production, this would call your Power BI API
      const reportData = {
        reportId: `powerbi_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        reportName: reportTitle,
        embedUrl: this.generateMockEmbedUrl(chartData, reportTitle),
        accessToken: await this.getMockAccessToken(),
        datasetId: `dataset_${Date.now()}`,
        workspaceId: this.powerBIConfig.workspaceId,
        chartData: chartData,
        metadata: {
          ...metadata,
          generatedAt: new Date().toISOString(),
          reportType: 'interactive_dashboard',
          dataPoints: chartData?.values?.length || 0
        },
        expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour
        settings: {
          filterPaneEnabled: true,
          navContentPaneEnabled: true,
          background: 'transparent'
        }
      };

      console.log('🔷 Power BI report generated:', reportData);
      return {
        success: true,
        report: reportData,
        message: 'Power BI report generated successfully'
      };

    } catch (error) {
      console.error('❌ Error generating Power BI report:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to generate Power BI report'
      };
    }
  }

  /**
   * Get access token for Power BI embedding
   * @returns {Promise<string>} Access token
   */
  async getMockAccessToken() {
    // In production, this would authenticate with Power BI and get a real token
    return `mock_token_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Generate mock embed URL for demonstration
   * @param {Object} chartData - Chart data
   * @param {string} title - Report title
   * @returns {string} Mock embed URL
   */
  generateMockEmbedUrl(chartData, title) {
    const params = new URLSearchParams({
      reportId: `report_${Date.now()}`,
      title: encodeURIComponent(title),
      dataPoints: chartData?.values?.length || 0,
      chartType: chartData?.type || 'donut',
      timestamp: Date.now()
    });

    return `${this.powerBIConfig.embedUrl}?${params.toString()}`;
  }

  /**
   * Create Power BI report configuration for embedding
   * @param {Object} reportData - Report data from generateReport
   * @returns {Object} Power BI embed configuration
   */
  createEmbedConfig(reportData) {
    return {
      type: 'report',
      id: reportData.reportId,
      embedUrl: reportData.embedUrl,
      accessToken: reportData.accessToken,
      tokenType: 1, // Embed token
      settings: {
        panes: {
          filters: {
            expanded: false,
            visible: reportData.settings.filterPaneEnabled
          },
          pageNavigation: {
            visible: reportData.settings.navContentPaneEnabled
          }
        },
        background: reportData.settings.background,
        layoutType: 1, // Custom layout
        customLayout: {
          displayOption: 1 // Fit to page
        }
      },
      permissions: 1, // Read permissions
      viewMode: 1, // View mode
      datasetBinding: {
        datasetId: reportData.datasetId
      }
    };
  }

  /**
   * Generate Power BI dashboard URL for external viewing
   * @param {Object} reportData - Report data
   * @returns {string} Dashboard URL
   */
  generateDashboardUrl(reportData) {
    const baseUrl = 'https://app.powerbi.com/groups';
    return `${baseUrl}/${reportData.workspaceId}/reports/${reportData.reportId}/ReportSection`;
  }

  /**
   * Check if Power BI is available and configured
   * @returns {boolean} True if Power BI is available
   */
  isAvailable() {
    // In production, this would check if Power BI credentials are configured
    return true; // For demo purposes
  }

  /**
   * Get Power BI report status
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Report status
   */
  async getReportStatus(reportId) {
    try {
      // Mock status check
      return {
        reportId,
        status: 'ready',
        lastUpdated: new Date().toISOString(),
        isExpired: false,
        viewCount: Math.floor(Math.random() * 100)
      };
    } catch (error) {
      console.error('❌ Error checking report status:', error);
      return {
        reportId,
        status: 'error',
        error: error.message
      };
    }
  }

  /**
   * Refresh Power BI report data
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Refresh result
   */
  async refreshReport(reportId) {
    try {
      console.log('🔄 Refreshing Power BI report:', reportId);
      
      // Mock refresh
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      return {
        success: true,
        reportId,
        refreshedAt: new Date().toISOString(),
        message: 'Report refreshed successfully'
      };
    } catch (error) {
      console.error('❌ Error refreshing report:', error);
      return {
        success: false,
        reportId,
        error: error.message
      };
    }
  }

  /**
   * Delete Power BI report
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteReport(reportId) {
    try {
      console.log('🗑️ Deleting Power BI report:', reportId);
      
      // Mock deletion
      return {
        success: true,
        reportId,
        deletedAt: new Date().toISOString(),
        message: 'Report deleted successfully'
      };
    } catch (error) {
      console.error('❌ Error deleting report:', error);
      return {
        success: false,
        reportId,
        error: error.message
      };
    }
  }
}

export default new PowerBIService();
