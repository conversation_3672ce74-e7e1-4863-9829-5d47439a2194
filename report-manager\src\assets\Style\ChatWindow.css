/* Full Space Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow: hidden;
}

/* Simple Header */
.chat-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.company-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-info h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar-header {
  width: 32px;
  height: 32px;
  background: #6b7280;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

/* Welcome View - Full Width */
.welcome-view {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 0;
  width: 100%;
}

.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.brand-title {
  font-size: 48px;
  font-weight: 400;
  color: #374151;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

/* Welcome screen input container */
.welcome-input-container {
  width: 100%;
  max-width: 98vw;
}

/* Large welcome input wrapper */
.welcome-input-wrapper-large {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 32px;
  padding: 20px 28px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 8px;
  min-height: 100px;
  max-width: 1000px;
  width: 95%;
  margin-left: auto;
  margin-right: auto;
}

.welcome-input-wrapper-large:focus-within {
  border-color: #9ca3af;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Large input styling for welcome screen */
.chat-input-large {
  width: 100%;
  border: none;
  outline: none;
  font-size: 18px;
  color: #374151;
  background: transparent;
  padding: 12px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  resize: none;
  min-height: 40px;
  max-height: 200px;
  line-height: 1.5;
  margin-bottom: 14px;
}

.chat-input-large::placeholder {
  color: #9ca3af;
  font-size: 18px;
}

/* Tools positioned below input area */
.welcome-tools-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

.left-tools,
.right-tools {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: none;
  color: #6b7280;
  font-size: 16px;
  padding: 8px 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.tool-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.tool-icon {
  font-size: 16px;
}

.welcome-disclaimer {
  text-align: center;
  padding: 12px 20px;
}

.welcome-disclaimer p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}
  letter-spacing: -0.025em;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  text-align: center;
}

.search-container {
  width: 100%;
  max-width: 900px;
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  border: 1px solid #e2e8f0;
  border-radius: 32px;
  padding: 24px 28px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  min-height: 120px;
  width: 100%;
}

.search-input-wrapper:focus-within {
  border-color: #cbd5e0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
}

.input-section {
  flex: 1;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 18px;
  color: #2d3748;
  background: transparent;
  padding: 12px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  line-height: 1.5;
}

.search-input::placeholder {
  color: #718096;
  font-weight: 400;
  font-size: 18px;
}

.actions-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-icon {
  flex-shrink: 0;
  padding: 8px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 2px solid #cbd5e0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 18px;
}

/* Simple Action Buttons */
.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  font-size: 16px;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.voice-btn.listening {
  background-color: #4299e1;
  color: white;
  border-color: #4299e1;
}

.submit-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #4299e1;
  border-radius: 8px;
  background-color: #4299e1;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 18px;
}

.submit-btn:disabled {
  background-color: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
}

.submit-btn.active:not(:disabled) {
  background-color: #3182ce;
}

.submit-btn:not(:disabled):hover {
  background-color: #3182ce;
}

/* Messages Area - ChatGPT Style */
.messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  width: 100%;
  background: #ffffff;
  min-height: 0;
}

.messages-area::-webkit-scrollbar {
  width: 6px;
}

.messages-area::-webkit-scrollbar-track {
  background: transparent;
}

.messages-area::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.messages-area::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.no-messages {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
}

.welcome-message {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.welcome-message h3 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
}

.welcome-message p {
  margin: 0;
  color: #718096;
  font-size: 16px;
  line-height: 1.5;
}

/* ChatGPT-style Message Interface */
.message {
  display: flex;
  width: 100%;
  padding: 12px 20px;
}

.message.user {
  justify-content: flex-end;
  background-color: transparent;
}

.message.assistant {
  justify-content: flex-start;
  background-color: #ffffff;
}

/* ChatGPT User Message Styles */
.user-message {
  display: flex;
  justify-content: flex-end;
  max-width: 768px;
  margin: 0 auto;
  width: 100%;
}

.user-content {
  background: #f7f7f8;
  color: #1f2937;
  padding: 12px 16px;
  border-radius: 18px;
  max-width: 70%;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
}

.user-text {
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}

/* User Message Edit Functionality */
.user-display-container {
  position: relative;
}

.user-message-actions {
  position: absolute;
  bottom: -40px;
  right: 0;
  display: flex;
  gap: 4px;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.user-action-btn {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: #9ca3af;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-action-btn:hover {
  background-color: #e5e7eb;
  border-color: #d1d5db;
  color: #6b7280;
}

.user-action-btn svg {
  width: 12px;
  height: 12px;
  stroke-width: 1.5;
}

/* User Message Edit Mode */
.user-edit-container {
  width: 100%;
}

.user-edit-textarea {
  width: 100%;
  min-height: 44px;
  max-height: 200px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
  outline: none;
  background: #ffffff;
  overflow-y: auto;
  box-sizing: border-box;
}

.user-edit-textarea:focus {
  border-color: #9ca3af;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
}

.user-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

.edit-action-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid transparent;
}

.cancel-btn {
  background: #ffffff;
  color: #6b7280;
  border-color: #d1d5db;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.save-btn {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

.save-btn:hover {
  background: #1f2937;
}

.save-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* ChatGPT Assistant Message Styles */
.assistant-message {
  display: flex;
  justify-content: center;
  max-width: 768px;
  margin: 0 auto;
  width: 100%;
}

.assistant-content {
  background: transparent;
  border: none;
  color: #1f2937;
  padding: 0;
  border-radius: 0;
  max-width: 100%;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
}

.assistant-text {
  font-size: 16px;
  line-height: 1.5;
  color: #1f2937;
  margin: 0;
}

/* Legacy support */
.message-content {
  color: #1f2937;
  line-height: 1.6;
}

.simple-text {
  color: #1f2937;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Segoe UI', system-ui, sans-serif;
}

.response-text {
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background: #f8fafc;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  max-height: 300px;
  overflow-y: auto;
}

.agents-used {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 6px;
  font-size: 12px;
  color: #0369a1;
}

.message-timestamp {
  margin-top: 4px;
  font-size: 11px;
  color: #9ca3af;
  text-align: right;
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 0;
}

.typing-dots {
  display: flex;
  gap: 4px;
  margin-right: 8px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  font-size: 16px;
  color: #6b7280;
  font-style: italic;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Input Area - ChatGPT Style */
.input-area {
  background: #ffffff;
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  flex-shrink: 0;
}

/* ChatGPT-style Input Container */
.input-container {
  max-width: 768px;
  margin: 0 auto;
  width: 100%;
  padding: 0 20px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 12px 16px;
  transition: border-color 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  min-height: 52px;
}

.input-wrapper:focus-within {
  border-color: #9ca3af;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: #374151;
  background: transparent;
  padding: 8px 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  resize: none;
  min-height: 24px;
  max-height: 200px;
  line-height: 1.5;
}

/* Send button specific styling for welcome screen */
.welcome-tools-bottom .send-btn {
  background: #000000 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  padding: 0 !important;
}

.welcome-tools-bottom .send-btn:hover:not(:disabled) {
  background: #333333 !important;
  transform: scale(1.05);
}

.welcome-tools-bottom .send-btn:disabled {
  background: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.chat-input::placeholder {
  color: #9ca3af;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

/* Left side actions (+ and Tools) */
.left-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
}

.add-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: background-color 0.2s ease;
}

.add-btn:hover {
  background: #f3f4f6;
}

.tools-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.tools-btn:hover {
  background: #f3f4f6;
}

.tools-icon {
  font-size: 16px;
}

/* Right side actions (voice and send) */
.right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Send button styling */
.send-btn {
  background: #000000 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  transition: all 0.2s ease !important;
}

.send-btn:hover:not(:disabled) {
  background: #333333 !important;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* ChatGPT-style disclaimer */
.disclaimer {
  text-align: center;
  padding: 8px 20px 12px;
  max-width: 768px;
  margin: 0 auto;
}

.disclaimer p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-view {
    padding: 16px;
  }

  .brand-title {
    font-size: 36px;
    margin-bottom: 16px;
  }

  .welcome-input-wrapper-large {
    padding: 18px 24px;
    min-height: 85px;
    border-radius: 28px;
    max-width: 96vw;
    width: 96%;
  }

  .chat-input-large {
    font-size: 16px;
    padding: 12px 0;
    min-height: 40px;
    margin-bottom: 12px;
  }

  .chat-input-large::placeholder {
    font-size: 16px;
  }

  .left-tools,
  .right-tools {
    gap: 8px;
  }

  .tool-btn {
    padding: 6px 10px;
    font-size: 14px;
  }

  .input-area {
    padding: 10px 16px;
  }

  .input-container {
    padding: 0 12px;
  }

  .messages-area {
    padding: 16px 12px;
  }

  .welcome-input-wrapper-large .left-actions {
    gap: 8px;
    margin-right: 12px;
  }

  .welcome-input-wrapper-large .tools-btn {
    padding: 6px 10px;
    font-size: 14px;
  }
}

/* Chart Link Button Styles */
.chart-link-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.chart-link-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.chart-link-btn:active {
  transform: translateY(0);
}

.chart-link-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

.chart-link-info span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Better space utilization */
.chat-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* Ensure proper flex behavior */
.welcome-view,
.messages-area {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
}

/* Follow-up input styles */
.followup-input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  border: 1px solid #e2e8f0;
  border-radius: 28px;
  padding: 14px 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  min-height: 75px;
  width: 60%;
  max-width: none;
}

.followup-input-wrapper:focus-within {
  border-color: #cbd5e0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
}

.followup-input-section {
  flex: 1;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.followup-input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 18px;
  color: #2d3748;
  background: transparent;
  padding: 8px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  line-height: 1.4;
}

.followup-input::placeholder {
  color: #718096;
  font-weight: 400;
  font-size: 18px;
}

.followup-actions-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 4px;
}

.followup-left-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.followup-right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.followup-search-icon {
  color: #718096;
  flex-shrink: 0;
  padding: 8px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 18px;
}

.followup-action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background-color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
  font-size: 18px;
}

.followup-action-btn:hover {
  background-color: #f7f8fa;
  border-color: #cbd5e0;
}

.followup-action-btn.voice-btn.listening {
  background-color: #4299e1;
  color: white;
  border-color: #4299e1;
}

.followup-submit-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background-color: #4299e1;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 18px;
}

.followup-submit-btn:disabled {
  background-color: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
}

.followup-submit-btn.active:not(:disabled) {
  background-color: #3182ce;
}

.followup-submit-btn:not(:disabled):hover {
  background-color: #3182ce;
}

/* User Avatar Tooltip Enhancement */
.user-avatar {
  position: relative;
  cursor: pointer;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(47, 47, 47, 0.3);
}

/* Fix Action Buttons Horizontal Layout */
.message-actions {
  margin-top: 12px;
  margin-bottom: 8px;
}

.action-buttons {
  display: flex !important;
  flex-direction: row !important;
  gap: 8px !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

/* Essential Action Icons - Clean Style */
.message-actions {
  margin-top: 8px;
  margin-bottom: 4px;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
}

/* Power BI Button Styling */
.action-btn.powerbi-btn {
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  border-color: #0078d4;
  color: white;
}

.action-btn.powerbi-btn:hover {
  background: linear-gradient(135deg, #106ebe 0%, #005a9e 100%);
  border-color: #106ebe;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 120, 212, 0.3);
}
  justify-content: flex-start;
}

.action-btn {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  padding: 4px;
  border-radius: 6px;
  cursor: pointer;
  color: #374151;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
}

.action-btn:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.action-btn:active {
  transform: scale(0.95) translateY(0);
  background-color: #f3f4f6;
}

.action-btn svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}
