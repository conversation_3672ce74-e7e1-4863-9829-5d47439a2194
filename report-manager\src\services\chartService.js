/**
 * Chart Service for handling chart data requests and transformations
 */

const API_BASE_URL = 'http://localhost:8000';

class ChartService {
  /**
   * Generate chart data from natural language query
   * @param {string} text - Natural language query
   * @param {string} chartType - Type of chart (donut, bar, line, pie)
   * @param {string} title - Chart title
   * @param {number} width - Chart width
   * @param {number} height - Chart height
   * @returns {Promise<Object>} Chart data response
   */
  async generateChart(text, chartType = 'donut', title = null, width = 400, height = 400) {
    try {
      const response = await fetch(`${API_BASE_URL}/charts/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          chart_type: chartType,
          title,
          width,
          height
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error generating chart:', error);
      return {
        success: false,
        charts: [],
        metadata: {},
        error: error.message
      };
    }
  }

  /**
   * Get employee salary distribution as donut chart
   * @returns {Promise<Object>} Salary donut chart data
   */
  async getSalaryDonutChart() {
    try {
      const response = await fetch(`${API_BASE_URL}/charts/salary-donut`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting salary donut chart:', error);
      return {
        success: false,
        charts: [],
        metadata: {},
        error: error.message
      };
    }
  }

  /**
   * Extract chart data from streaming API response
   * @param {Object} streamingData - Streaming API response data
   * @returns {Array} Array of chart objects or empty array
   */
  extractChartsFromStreamingData(streamingData) {
    const charts = [];

    if (!streamingData || !streamingData.chunks) {
      return charts;
    }

    // Look for chart data in streaming chunks
    streamingData.chunks.forEach(chunk => {
      if (chunk.type === 'chart' && chunk.chart) {
        charts.push(chunk.chart);
      }
    });

    return charts;
  }

  /**
   * Transform API response data to include charts (Updated for streaming)
   * @param {Object} apiResponse - Original API response
   * @param {string} query - Original query text
   * @returns {Object} Enhanced response with chart data
   */
  async enhanceResponseWithCharts(apiResponse, query) {
    try {
      // First check if charts are already in the streaming response
      if (apiResponse.streamingData) {
        const charts = this.extractChartsFromStreamingData(apiResponse.streamingData);
        if (charts.length > 0) {
          console.log('Charts found in streaming data:', charts);
          return {
            ...apiResponse,
            charts: charts,
            chartMetadata: {
              source: 'streaming_api',
              generated_at: new Date().toISOString(),
              query: query
            }
          };
        }
      }

      // Fallback: Check if the query is asking for charts and we need to generate them separately
      const chartKeywords = ['chart', 'graph', 'visualization', 'donut', 'pie', 'bar', 'plot'];
      const queryLower = query.toLowerCase();
      const needsChart = chartKeywords.some(keyword => queryLower.includes(keyword));

      if (!needsChart) {
        return apiResponse;
      }

      console.log('No charts in streaming data, but chart requested. Using fallback generation.');

      // Fallback to separate chart generation (for backward compatibility)
      const chartResponse = await this.generateChart(query, this.getChartTypeFromQuery(queryLower));

      if (chartResponse.success && chartResponse.charts.length > 0) {
        return {
          ...apiResponse,
          charts: chartResponse.charts,
          chartMetadata: {
            ...chartResponse.metadata,
            source: 'fallback_api'
          }
        };
      }

      return apiResponse;
    } catch (error) {
      console.error('Error enhancing response with charts:', error);
      return apiResponse;
    }
  }

  /**
   * Get chart type from query text
   * @param {string} queryLower - Lowercase query text
   * @returns {string} Chart type
   */
  getChartTypeFromQuery(queryLower) {
    if (queryLower.includes('bar')) return 'bar';
    if (queryLower.includes('pie')) return 'pie';
    if (queryLower.includes('donut') || queryLower.includes('doughnut')) return 'donut';
    return 'donut'; // default
  }

  /**
   * Transform tabular data to chart format
   * @param {Array} data - Array of data objects
   * @param {string} labelField - Field to use for labels
   * @param {string} valueField - Field to use for values
   * @param {string} chartType - Type of chart
   * @returns {Object} Chart data object
   */
  transformDataToChart(data, labelField, valueField, chartType = 'donut') {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return null;
    }

    const labels = data.map(item => item[labelField] || 'Unknown');
    const values = data.map(item => {
      let value = item[valueField] || 0;
      // Convert string numbers to actual numbers
      if (typeof value === 'string') {
        value = parseFloat(value.replace(/[,$]/g, '')) || 0;
      }
      return value;
    });

    const total = values.reduce((sum, val) => sum + val, 0);
    const average = total / values.length;

    return {
      labels,
      values,
      datasetLabel: valueField,
      summary: [
        { label: 'Total', value: total.toLocaleString() },
        { label: 'Average', value: average.toLocaleString() },
        { label: 'Count', value: data.length.toString() }
      ]
    };
  }

  /**
   * Get chart configuration based on query intent
   * @param {string} query - User query
   * @returns {Object} Chart configuration
   */
  getChartConfig(query) {
    const queryLower = query.toLowerCase();
    
    // Default configuration
    const config = {
      type: 'donut',
      width: 400,
      height: 400,
      title: null
    };

    // Determine chart type
    if (queryLower.includes('bar') || queryLower.includes('column')) {
      config.type = 'bar';
      config.width = 600;
    } else if (queryLower.includes('pie')) {
      config.type = 'pie';
    } else if (queryLower.includes('donut')) {
      config.type = 'donut';
    }

    // Determine size
    if (queryLower.includes('large') || queryLower.includes('big')) {
      config.width = config.width * 1.5;
      config.height = config.height * 1.5;
    } else if (queryLower.includes('small') || queryLower.includes('mini')) {
      config.width = config.width * 0.7;
      config.height = config.height * 0.7;
    }

    return config;
  }

  /**
   * Check if a query should generate charts
   * @param {string} query - User query
   * @returns {boolean} Whether charts should be generated
   */
  shouldGenerateChart(query) {
    const chartKeywords = [
      'chart', 'graph', 'visualization', 'donut', 'pie', 'bar', 'plot',
      'distribution', 'breakdown', 'visual', 'diagram'
    ];
    
    const queryLower = query.toLowerCase();
    return chartKeywords.some(keyword => queryLower.includes(keyword));
  }

  /**
   * Get sample chart data for testing
   * @param {string} type - Chart type
   * @returns {Object} Sample chart data
   */
  getSampleChartData(type = 'donut') {
    const sampleData = {
      donut: {
        labels: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance'],
        values: [2500000, 1800000, 1200000, 950000, 1600000],
        summary: [
          { label: 'Departments', value: '5' },
          { label: 'Total Budget', value: '$8.05M' },
          { label: 'Avg per Dept', value: '$1.61M' }
        ]
      },
      bar: {
        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
        values: [120000, 150000, 180000, 200000],
        datasetLabel: 'Revenue',
        summary: [
          { label: 'Total', value: '$650K' },
          { label: 'Average', value: '$162.5K' },
          { label: 'Growth', value: '67%' }
        ]
      }
    };

    return sampleData[type] || sampleData.donut;
  }
}

// Create and export a singleton instance
const chartService = new ChartService();
export default chartService;
