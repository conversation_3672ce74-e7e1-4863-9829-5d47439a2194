/**
 * Enhanced Excel Generator with Chart Embedding using ExcelJS
 */
import ExcelJS from 'exceljs';

class ExcelChartGenerator {
  /**
   * Generate Excel file with embedded charts
   * @param {Array} data - Array of objects to convert to Excel
   * @param {string} filename - Desired filename
   * @param {Object} chartData - Chart data to embed
   * @returns {Object} Download info with blob URL
   */
  static async generateWithChart(data, filename = 'report.xlsx', chartData = null) {
    try {
      console.log('📊 Generating Excel with embedded chart using ExcelJS...');
      console.log('📊 Chart data:', chartData);
      
      // Create a new workbook
      const workbook = new ExcelJS.Workbook();
      
      // Set workbook properties
      workbook.creator = 'Report Manager';
      workbook.lastModifiedBy = 'Report Manager';
      workbook.created = new Date();
      workbook.modified = new Date();
      
      // Add data worksheet
      const dataWorksheet = workbook.addWorksheet('Data');
      
      // Add headers
      if (data && data.length > 0) {
        const headers = Object.keys(data[0]);
        dataWorksheet.addRow(headers);
        
        // Style headers
        const headerRow = dataWorksheet.getRow(1);
        headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
        headerRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '366092' }
        };
        
        // Add data rows
        data.forEach(row => {
          const values = headers.map(header => row[header]);
          dataWorksheet.addRow(values);
        });
        
        // Auto-fit columns
        dataWorksheet.columns.forEach(column => {
          column.width = 15;
        });
      }
      
      // Add chart if chart data is provided
      if (chartData && chartData.data && chartData.data.labels && chartData.data.values) {
        console.log('📊 Adding chart to Excel...');
        
        // Create chart worksheet
        const chartWorksheet = workbook.addWorksheet('Chart');
        
        // Add chart data to worksheet
        chartWorksheet.addRow(['Category', 'Value', 'Percentage']);
        
        const total = chartData.data.values.reduce((a, b) => a + b, 0);
        chartData.data.labels.forEach((label, index) => {
          const value = chartData.data.values[index];
          const percentage = ((value / total) * 100).toFixed(1);
          chartWorksheet.addRow([label, value, `${percentage}%`]);
        });
        
        // Style the chart data
        const chartHeaderRow = chartWorksheet.getRow(1);
        chartHeaderRow.font = { bold: true, color: { argb: 'FFFFFF' } };
        chartHeaderRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '366092' }
        };
        
        // Auto-fit columns
        chartWorksheet.columns.forEach(column => {
          column.width = 15;
        });
        
        // Add chart title
        chartWorksheet.mergeCells('A' + (chartData.data.labels.length + 4) + ':C' + (chartData.data.labels.length + 4));
        const titleCell = chartWorksheet.getCell('A' + (chartData.data.labels.length + 4));
        titleCell.value = chartData.title || 'Chart';
        titleCell.font = { bold: true, size: 16 };
        titleCell.alignment = { horizontal: 'center' };
        
        // Add instructions for creating chart in Excel
        const instructionRow = chartData.data.labels.length + 6;
        chartWorksheet.mergeCells(`A${instructionRow}:C${instructionRow}`);
        const instructionCell = chartWorksheet.getCell(`A${instructionRow}`);
        instructionCell.value = 'To create a chart: Select data above → Insert → Charts → Doughnut Chart';
        instructionCell.font = { italic: true, color: { argb: '666666' } };
        
        // Try to add an actual chart (ExcelJS chart support)
        try {
          // Note: ExcelJS has limited chart support, but we can try
          const chartRange = `A1:B${chartData.data.labels.length + 1}`;
          
          // Add a simple chart instruction
          const chartInstructionRow = instructionRow + 2;
          chartWorksheet.mergeCells(`A${chartInstructionRow}:C${chartInstructionRow + 2}`);
          const chartInstructionCell = chartWorksheet.getCell(`A${chartInstructionRow}`);
          chartInstructionCell.value = `Chart Type: ${chartData.type.toUpperCase()}\nData Range: ${chartRange}\nTotal Data Points: ${chartData.data.labels.length}`;
          chartInstructionCell.alignment = { wrapText: true, vertical: 'top' };
          
        } catch (chartError) {
          console.warn('⚠️ Could not add chart object, but chart data is available:', chartError);
        }
      }
      
      // Add summary worksheet
      const summaryWorksheet = workbook.addWorksheet('Summary');
      
      // Add summary information
      const summaryData = [
        ['Report Information', ''],
        ['Generated At', new Date().toLocaleString()],
        ['Filename', filename],
        ['Data Records', data ? data.length : 0],
        ['', ''],
        ['Chart Information', ''],
        ['Chart Type', chartData?.type || 'N/A'],
        ['Chart Title', chartData?.title || 'N/A'],
        ['Data Points', chartData?.data?.labels?.length || 0],
      ];
      
      if (chartData?.data?.summary) {
        summaryData.push(['', '']);
        summaryData.push(['Statistics', '']);
        chartData.data.summary.forEach(item => {
          summaryData.push([item.label, item.value]);
        });
      }
      
      summaryData.forEach(row => {
        summaryWorksheet.addRow(row);
      });
      
      // Style summary
      summaryWorksheet.getColumn(1).width = 20;
      summaryWorksheet.getColumn(2).width = 25;
      
      // Generate Excel buffer
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      // Create download URL
      const downloadUrl = URL.createObjectURL(blob);
      
      console.log('✅ Excel file with chart generated successfully');
      
      return {
        downloadUrl,
        filename,
        size: blob.size,
        timestamp: new Date().toISOString(),
        type: 'excel',
        hasChartData: !!chartData,
        hasEmbeddedChart: true
      };
      
    } catch (error) {
      console.error('❌ Error generating Excel with chart:', error);
      throw error;
    }
  }
  
  /**
   * Generate Excel with chart visualization using cell formatting
   * @param {Array} data - Data array
   * @param {string} filename - Filename
   * @param {Object} chartData - Chart data
   * @returns {Object} Download info
   */
  static async generateWithVisualChart(data, filename = 'report.xlsx', chartData = null) {
    try {
      console.log('📊 Generating Excel with visual chart representation...');
      
      const workbook = new ExcelJS.Workbook();
      
      // Add data sheet
      const dataSheet = workbook.addWorksheet('Data');
      
      if (data && data.length > 0) {
        const headers = Object.keys(data[0]);
        dataSheet.addRow(headers);
        data.forEach(row => {
          const values = headers.map(header => row[header]);
          dataSheet.addRow(values);
        });
      }
      
      // Add visual chart sheet
      if (chartData && chartData.data) {
        const chartSheet = workbook.addWorksheet('Visual Chart');
        
        // Create a visual representation using cells
        chartSheet.addRow([chartData.title || 'Chart']);
        chartSheet.addRow(['']);
        
        const total = chartData.data.values.reduce((a, b) => a + b, 0);
        
        chartData.data.labels.forEach((label, index) => {
          const value = chartData.data.values[index];
          const percentage = ((value / total) * 100).toFixed(1);
          
          // Add label and value
          chartSheet.addRow([label, value, `${percentage}%`]);
          
          // Add visual bar representation
          const barLength = Math.round((value / total) * 20); // Scale to 20 characters
          const bar = '█'.repeat(barLength) + '░'.repeat(20 - barLength);
          chartSheet.addRow(['', bar, '']);
        });
        
        // Style the visual chart
        chartSheet.getColumn(1).width = 20;
        chartSheet.getColumn(2).width = 25;
        chartSheet.getColumn(3).width = 10;
      }
      
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      const downloadUrl = URL.createObjectURL(blob);
      
      return {
        downloadUrl,
        filename,
        size: blob.size,
        timestamp: new Date().toISOString(),
        type: 'excel',
        hasChartData: !!chartData,
        hasVisualChart: true
      };
      
    } catch (error) {
      console.error('❌ Error generating visual chart Excel:', error);
      throw error;
    }
  }
}

export default ExcelChartGenerator;
