"""
Chart API endpoints for generating chart data from database queries
"""
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from ...core.database_query_engine import DatabaseQueryEngine
from ...core.session import UserSession, get_user_session

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/charts", tags=["charts"])

# Global database query engine instance
db_query_engine: Optional[DatabaseQueryEngine] = None

def set_db_query_engine(engine: DatabaseQueryEngine):
    """Set the global database query engine instance"""
    global db_query_engine
    db_query_engine = engine

class ChartRequest(BaseModel):
    text: str
    chart_type: str = "donut"  # donut, bar, line, pie
    title: Optional[str] = None
    width: Optional[int] = 400
    height: Optional[int] = 400

class ChartDataResponse(BaseModel):
    success: bool
    charts: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    error: Optional[str] = None

@router.post("/generate", response_model=ChartDataResponse)
async def generate_chart(
    request: ChartRequest,
    session: UserSession = Depends(get_user_session)
):
    """
    Generate chart data from natural language query
    """
    try:
        logger.info(f"🎯 Chart generation request: {request.text}")
        
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        # Execute the query to get data
        result = await db_query_engine.execute_query(request.text, session)
        
        if not result.success:
            return ChartDataResponse(
                success=False,
                charts=[],
                metadata={},
                error=f"Query failed: {result.error}"
            )

        # Transform data for chart format
        chart_data = transform_data_for_chart(
            result.data, 
            result.columns, 
            request.chart_type,
            request.title or f"Chart: {request.text}"
        )
        
        if not chart_data:
            return ChartDataResponse(
                success=False,
                charts=[],
                metadata={},
                error="No suitable data found for chart generation"
            )

        return ChartDataResponse(
            success=True,
            charts=[{
                "type": request.chart_type,
                "title": request.title or f"Chart: {request.text}",
                "data": chart_data,
                "options": {
                    "width": request.width,
                    "height": request.height
                }
            }],
            metadata={
                "query": request.text,
                "sql_query": result.sql_query,
                "datasource": result.datasource,
                "execution_time": result.execution_time,
                "generated_at": datetime.now().isoformat(),
                "user": session.user_id
            }
        )

    except Exception as e:
        logger.error(f"Error generating chart: {e}")
        return ChartDataResponse(
            success=False,
            charts=[],
            metadata={},
            error=str(e)
        )

@router.get("/salary-donut")
async def get_salary_donut_chart(session: UserSession = Depends(get_user_session)):
    """
    Get employee salary distribution as donut chart
    """
    try:
        if not db_query_engine:
            raise HTTPException(status_code=500, detail="Database engine not initialized")

        # Query for salary distribution by department
        query = "Show me employee salary distribution by department"
        result = await db_query_engine.execute_query(query, session)
        
        if not result.success:
            # Return mock data if query fails
            chart_data = {
                "labels": ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations"],
                "values": [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
                "summary": [
                    {"label": "Departments", "value": "6"},
                    {"label": "Avg Salary", "value": "$85,000"}
                ]
            }
        else:
            # Transform real data
            chart_data = transform_salary_data_for_donut(result.data, result.columns)

        return ChartDataResponse(
            success=True,
            charts=[{
                "type": "donut",
                "title": "Employee Salary Distribution by Department",
                "data": chart_data,
                "options": {
                    "width": 500,
                    "height": 400
                }
            }],
            metadata={
                "query": "Employee salary distribution by department",
                "generated_at": datetime.now().isoformat(),
                "user": session.user_id
            }
        )

    except Exception as e:
        logger.error(f"Error generating salary donut chart: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def transform_data_for_chart(data: List[Dict], columns: List[str], chart_type: str, title: str) -> Optional[Dict]:
    """
    Transform query result data into chart format
    """
    if not data or len(data) == 0:
        return None
    
    # Try to find label and value columns
    label_col = None
    value_col = None
    
    for col in columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['name', 'department', 'category', 'type', 'label']):
            label_col = col
        elif any(keyword in col_lower for keyword in ['salary', 'amount', 'value', 'count', 'total', 'sum']):
            value_col = col
    
    # If we can't find appropriate columns, use first two columns
    if not label_col and len(columns) > 0:
        label_col = columns[0]
    if not value_col and len(columns) > 1:
        value_col = columns[1]
    elif not value_col and len(columns) == 1:
        # Single column data - create count chart
        value_counts = {}
        for row in data:
            val = str(row[label_col])
            value_counts[val] = value_counts.get(val, 0) + 1
        
        return {
            "labels": list(value_counts.keys()),
            "values": list(value_counts.values()),
            "datasetLabel": "Count"
        }
    
    if not label_col or not value_col:
        return None
    
    # Extract labels and values
    labels = []
    values = []
    
    for row in data:
        label = str(row.get(label_col, ''))
        value = row.get(value_col, 0)
        
        # Convert value to number if it's a string
        if isinstance(value, str):
            try:
                value = float(value.replace(',', '').replace('$', ''))
            except:
                value = 0
        
        labels.append(label)
        values.append(value)
    
    return {
        "labels": labels,
        "values": values,
        "datasetLabel": value_col
    }

def transform_salary_data_for_donut(data: List[Dict], columns: List[str]) -> Dict:
    """
    Transform salary data specifically for donut chart
    """
    if not data:
        # Return default data
        return {
            "labels": ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations"],
            "values": [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
            "summary": [
                {"label": "Departments", "value": "6"},
                {"label": "Avg Salary", "value": "$85,000"}
            ]
        }
    
    # Group by department and sum salaries
    dept_totals = {}
    total_employees = len(data)
    total_salary = 0
    
    for row in data:
        dept = row.get('Department', row.get('department', 'Unknown'))
        salary = row.get('Salary', row.get('salary', 0))
        
        if isinstance(salary, str):
            try:
                salary = float(salary.replace(',', '').replace('$', ''))
            except:
                salary = 0
        
        dept_totals[dept] = dept_totals.get(dept, 0) + salary
        total_salary += salary
    
    labels = list(dept_totals.keys())
    values = list(dept_totals.values())
    avg_salary = total_salary / total_employees if total_employees > 0 else 0
    
    return {
        "labels": labels,
        "values": values,
        "summary": [
            {"label": "Departments", "value": str(len(labels))},
            {"label": "Total Employees", "value": str(total_employees)},
            {"label": "Avg Salary", "value": f"${avg_salary:,.0f}"}
        ]
    }
