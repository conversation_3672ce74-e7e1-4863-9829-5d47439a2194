
import { useState } from 'react';
import Sidebar from './components/Sidebar';
import ChatInterface from './components/ChatInterface';
import './App.css';

function App() {
  const [conversations, setConversations] = useState([
    { id: 1, title: 'Report Analysis', messages: [] }
  ]);
  const [activeConversation, setActiveConversation] = useState(1);

  const handleNewChat = () => {
    const newId = Date.now();
    const newConv = { 
      id: newId, 
      title: 'New Chat', 
      messages: [] 
    };
    setConversations([...conversations, newConv]);
    setActiveConversation(newId);
  };

  const handleSendMessage = (message) => {
    const updatedConversations = conversations.map(conv => {
      if (conv.id === activeConversation) {
        const newMessages = [
          ...conv.messages,
          { type: 'user', text: message },
          { type: 'assistant', text: generateResponse(message) }
        ];
        return { ...conv, messages: newMessages };
      }
      return conv;
    });
    setConversations(updatedConversations);
  };

  const generateResponse = (message) => {
    // Simple response logic - replace with actual AI integration
    if (message.toLowerCase().includes('report')) {
      return 'I can help you with report generation, analysis, and management. What specific report do you need assistance with?';
    }
    return 'I\'m your reporting manager assistant. I can help with reports, analytics, and data insights. How can I assist you today?';
  };

  const currentConversation = conversations.find(conv => conv.id === activeConversation);

  return (
    <div className="app">
      <Sidebar
        conversations={conversations}
        activeConversation={activeConversation}
        onNewChat={handleNewChat}
        onSelectConversation={setActiveConversation}
      />
      <ChatInterface
        conversation={currentConversation}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
}

export default App;
