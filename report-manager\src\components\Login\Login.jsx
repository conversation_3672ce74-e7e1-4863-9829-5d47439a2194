import React, { useState, useEffect } from 'react';
import authService from '../../services/authService';
import './Login.css';

const Login = ({ onLoginSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsInitializing(true);
      await authService.initialize();
      
      // Check if user is already authenticated
      const existingUser = await authService.checkExistingAuth();
      if (existingUser && onLoginSuccess) {
        onLoginSuccess(existingUser);
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setError('Failed to initialize authentication. Please refresh the page.');
    } finally {
      setIsInitializing(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      await authService.signIn();
      
      // The actual sign-in completion is handled by the authService callback
      // We'll listen for the auth state change event
    } catch (error) {
      console.error('Sign-in failed:', error);
      setError(error.message || 'Sign-in failed. Please try again.');
      setIsLoading(false);
    }
  };

  // Listen for auth state changes
  useEffect(() => {
    const handleAuthStateChange = (event) => {
      const { isAuthenticated, user } = event.detail;
      
      if (isAuthenticated && user) {
        setIsLoading(false);
        setError(null);
        if (onLoginSuccess) {
          onLoginSuccess(user);
        }
      }
    };

    window.addEventListener('authStateChange', handleAuthStateChange);
    
    return () => {
      window.removeEventListener('authStateChange', handleAuthStateChange);
    };
  }, [onLoginSuccess]);

  if (isInitializing) {
    return (
      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <div className="app-logo">📊</div>
            <h1 className="app-title">Report Manager</h1>
            <p className="app-subtitle">Initializing authentication...</p>
          </div>
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="app-logo">📊</div>
          <h1 className="app-title">Report Manager</h1>
          <p className="app-subtitle">
            Generate and download reports with AI-powered insights
          </p>
        </div>

        <div className="login-content">
          <div className="login-benefits">
            <div className="benefit-item">
              <span className="benefit-icon">🤖</span>
              <span className="benefit-text">AI-powered data analysis</span>
            </div>
            <div className="benefit-item">
              <span className="benefit-icon">📈</span>
              <span className="benefit-text">Interactive Excel reports</span>
            </div>
            <div className="benefit-item">
              <span className="benefit-icon">🔒</span>
              <span className="benefit-text">Secure Google authentication</span>
            </div>
          </div>

          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              <span className="error-text">{error}</span>
            </div>
          )}

          <button
            className={`google-signin-btn ${isLoading ? 'loading' : ''}`}
            onClick={handleGoogleSignIn}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="btn-spinner"></div>
                <span>Signing in...</span>
              </>
            ) : (
              <>
                <svg className="google-icon" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span>Continue with Google</span>
              </>
            )}
          </button>

          <div className="login-footer">
            <p className="privacy-text">
              By signing in, you agree to our terms of service and privacy policy.
              Your data is protected and never shared with third parties.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
