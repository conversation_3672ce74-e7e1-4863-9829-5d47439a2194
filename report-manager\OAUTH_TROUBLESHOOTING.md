# OAuth Troubleshooting Guide

## Current Status
✅ OAuth configuration is properly set up
✅ Demo mode is disabled
✅ Google Client ID is configured
✅ Environment variables are correct

## Common Issues and Solutions

### 1. Google Cloud Console Configuration

**Check these settings in Google Cloud Console:**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to "APIs & Services" > "Credentials"
3. Find your OAuth 2.0 Client ID: `***********-lr3mkvm59r5nvofonf6otnm5ju6oa6tq.apps.googleusercontent.com`

**Required Settings:**
- **Application type**: Web application
- **Authorized JavaScript origins**:
  - `http://localhost:3003`
  - `http://127.0.0.1:3003`
- **Authorized redirect URIs**:
  - `http://localhost:3003`
  - `http://localhost:3003/`

### 2. Browser Console Errors

**Check for these common errors:**

1. **"Not a valid origin for the client"**
   - Solution: Add `http://localhost:3003` to authorized origins in Google Console

2. **"Popup blocked"**
   - Solution: Allow popups for localhost:3003 in browser settings

3. **"Cross-origin request blocked"**
   - Solution: Ensure CORS is properly configured

### 3. Network Issues

**Check if Google services are accessible:**
- Open browser dev tools (F12)
- Go to Network tab
- Try to sign in and check for failed requests to `accounts.google.com`

### 4. Browser Compatibility

**Supported browsers:**
- Chrome (recommended)
- Firefox
- Safari
- Edge

**Not supported:**
- Internet Explorer
- Very old browser versions

## Testing Steps

1. **Open browser console** (F12 → Console tab)
2. **Navigate to** http://localhost:3001
3. **Look for debug messages** starting with "🔐 AuthService:"
4. **Click "Continue with Google"** button
5. **Check for any error messages**

## Quick Fixes

### Fix 1: Clear Browser Data
```bash
# Clear localStorage and cookies for localhost:3001
# In browser console:
localStorage.clear();
# Then refresh the page
```

### Fix 2: Test with Different Browser
Try opening the app in an incognito/private window or different browser.

### Fix 3: Check Google Console Settings
Verify that your OAuth client is configured for "Web application" type.

## Debug Information

Current configuration:
- Client ID: `***********-lr3mkvm59r5nvofonf6otnm5ju6oa6tq.apps.googleusercontent.com`
- Redirect URI: `http://localhost:3003`
- Demo Mode: `false`

## Contact Information

If OAuth is still not working after following this guide:
1. Check browser console for specific error messages
2. Verify Google Cloud Console settings
3. Try the test OAuth page: http://localhost:3003/test-oauth.html
