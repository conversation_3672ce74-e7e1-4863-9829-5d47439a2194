"""
Logging Configuration for Report Manager Orchestrator

This module provides centralized logging configuration.
"""

import sys
from pathlib import Path
from loguru import logger
from .config import config


def setup_logging():
    """Setup logging configuration"""

    # Remove default logger
    logger.remove()

    # Get logging configuration
    log_level = config.get("logging.level", "INFO")
    log_file = config.get("logging.file", "orchestrator.log")

    # Console logging
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )

    # File logging
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="1 day",
        retention="7 days",
        compression="zip"
    )

    logger.info("Logging configured successfully")


def get_logger(name: str):
    """
    Get a logger instance

    Args:
        name: Logger name

    Returns:
        Logger instance
    """
    return logger.bind(name=name)