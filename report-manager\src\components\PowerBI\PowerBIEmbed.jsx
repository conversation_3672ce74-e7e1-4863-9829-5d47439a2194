import React, { useState, useEffect, useRef } from 'react';
import './PowerBIEmbed.css';

/**
 * Power BI Embed Component
 * Displays embedded Power BI reports in the chat interface
 */
const PowerBIEmbed = ({ 
  reportData, 
  onLoad, 
  onError, 
  height = '400px',
  width = '100%' 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const iframeRef = useRef(null);

  useEffect(() => {
    if (reportData) {
      console.log('🔷 Loading Power BI report:', reportData);
      setIsLoading(true);
      setError(null);
      
      // Simulate loading time
      const loadTimer = setTimeout(() => {
        setIsLoading(false);
        if (onLoad) {
          onLoad(reportData);
        }
      }, 2000);

      return () => clearTimeout(loadTimer);
    }
  }, [reportData, onLoad]);

  const handleIframeLoad = () => {
    console.log('🔷 Power BI iframe loaded');
    setIsLoading(false);
  };

  const handleIframeError = (error) => {
    console.error('❌ Power BI iframe error:', error);
    setError('Failed to load Power BI report');
    setIsLoading(false);
    if (onError) {
      onError(error);
    }
  };

  const openInNewTab = () => {
    if (reportData?.embedUrl) {
      window.open(reportData.embedUrl, '_blank');
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const refreshReport = () => {
    setIsLoading(true);
    setError(null);
    
    // Simulate refresh
    setTimeout(() => {
      setIsLoading(false);
      console.log('🔄 Power BI report refreshed');
    }, 1500);
  };

  if (!reportData) {
    return (
      <div style={{
        padding: '20px',
        border: '2px solid red',
        backgroundColor: '#ffe6e6',
        borderRadius: '8px',
        margin: '10px 0'
      }}>
        <div style={{ fontSize: '20px' }}>⚠️</div>
        <div>No Power BI report data provided</div>
      </div>
    );
  }

  console.log('🔷 PowerBIEmbed rendering with data:', reportData);

  return (
    <div style={{
      border: '2px solid #0078d4',
      borderRadius: '12px',
      backgroundColor: '#ffffff',
      margin: '16px 0',
      minHeight: height,
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '12px 16px',
        background: 'linear-gradient(135deg, #0078d4 0%, #106ebe 100%)',
        color: 'white',
        borderRadius: '10px 10px 0 0'
      }}>
        <div className="powerbi-title">
          <div className="powerbi-icon">🔷</div>
          <span className="title-text">{reportData.reportName || 'Power BI Report'}</span>
          {reportData.metadata?.dataPoints && (
            <span className="data-points">({reportData.metadata.dataPoints} data points)</span>
          )}
        </div>
        
        <div className="powerbi-actions">
          <button 
            className="action-btn refresh-btn"
            onClick={refreshReport}
            title="Refresh Report"
            disabled={isLoading}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="23 4 23 10 17 10"></polyline>
              <polyline points="1 20 1 14 7 14"></polyline>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
            </svg>
          </button>
          
          <button 
            className="action-btn expand-btn"
            onClick={toggleExpanded}
            title={isExpanded ? 'Collapse' : 'Expand'}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              {isExpanded ? (
                <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path>
              ) : (
                <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"></path>
              )}
            </svg>
          </button>
          
          <button 
            className="action-btn external-btn"
            onClick={openInNewTab}
            title="Open in Power BI"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
              <polyline points="15,3 21,3 21,9"></polyline>
              <line x1="10" y1="14" x2="21" y2="3"></line>
            </svg>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="powerbi-content" style={{ height: isExpanded ? '600px' : height }}>
        {isLoading && (
          <div className="powerbi-loading">
            <div className="loading-spinner"></div>
            <div className="loading-text">Loading Power BI Report...</div>
            <div className="loading-subtext">Connecting to Power BI service</div>
          </div>
        )}

        {error && (
          <div className="powerbi-error">
            <div className="error-icon">❌</div>
            <div className="error-message">{error}</div>
            <button className="retry-btn" onClick={refreshReport}>
              Try Again
            </button>
          </div>
        )}

        {!isLoading && !error && (
          <div style={{ minHeight: '350px', padding: '20px' }}>
            {/* Debug info */}
            {console.log('PowerBI Debug:', {
              hasEmbedUrl: !!reportData.embedUrl,
              embedUrl: reportData.embedUrl,
              isRealPowerBI: reportData.embedUrl && reportData.embedUrl.includes('app.powerbi.com') && !reportData.embedUrl.includes('reportEmbed?reportId=report_'),
              reportData: reportData
            })}

            {/* Add a note about mock vs real embedding */}
            <div style={{
              marginBottom: '10px',
              padding: '8px',
              backgroundColor: '#e3f2fd',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#1976d2'
            }}>
              {reportData.embedUrl && reportData.embedUrl.includes('app.powerbi.com') && !reportData.embedUrl.includes('reportEmbed?reportId=report_')
                ? '🔷 Real Power BI embedding mode'
                : '🔷 Mock Power BI display mode (for demonstration)'}
            </div>

            {/* Real Power BI Embed */}
            {reportData.embedUrl && reportData.embedUrl.includes('app.powerbi.com') && !reportData.embedUrl.includes('reportEmbed?reportId=report_') ? (
              <div style={{
                width: '100%',
                height: '600px',
                border: '1px solid #ccc',
                backgroundColor: '#f0f0f0'
              }}>
                <iframe
                  ref={iframeRef}
                  src={reportData.embedUrl}
                  width="100%"
                  height="100%"
                  frameBorder="0"
                  allowFullScreen={true}
                  onLoad={handleIframeLoad}
                  onError={handleIframeError}
                  title={`Power BI Report: ${reportData.reportName}`}
                  style={{ border: 'none' }}
                />
              </div>
            ) : (
              /* Simplified Power BI Report Display */
              <div style={{
                minHeight: '350px',
                border: '2px solid #ddd',
                backgroundColor: '#f8f9fa',
                padding: '20px',
                borderRadius: '8px'
              }}>
                {/* Simple Header */}
                <div style={{
                  marginBottom: '20px',
                  borderBottom: '2px solid #0078d4',
                  paddingBottom: '10px'
                }}>
                  <h3 style={{ margin: '0', color: '#0078d4', fontSize: '18px' }}>
                    🔷 {reportData.reportName || 'Power BI Dashboard'}
                  </h3>
                  <div style={{ marginTop: '8px' }}>
                    <span style={{
                      backgroundColor: '#e3f2fd',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      marginRight: '8px'
                    }}>All Departments</span>
                    <span style={{
                      backgroundColor: '#e3f2fd',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px'
                    }}>Current Year</span>
                  </div>
                </div>

                {/* Simple Chart Content */}
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#323130', marginBottom: '16px' }}>
                    📊 {reportData.chartData?.datasetLabel || 'Data Visualization'}
                  </h4>
                  <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '8px', border: '1px solid #e1e5e9' }}>
                    {reportData.chartData?.labels?.map((label, index) => (
                      <div key={index} style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: '8px',
                        padding: '8px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px'
                      }}>
                        <div style={{
                          minWidth: '120px',
                          fontWeight: '500',
                          color: '#323130'
                        }}>{label}</div>
                        <div style={{
                          flex: 1,
                          height: '20px',
                          backgroundColor: '#e1e5e9',
                          borderRadius: '10px',
                          margin: '0 10px',
                          position: 'relative'
                        }}>
                          <div style={{
                            width: `${(reportData.chartData.values[index] / Math.max(...reportData.chartData.values)) * 100}%`,
                            height: '100%',
                            backgroundColor: `hsl(${(index * 60) % 360}, 70%, 50%)`,
                            borderRadius: '10px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}>
                            {typeof reportData.chartData.values[index] === 'number'
                              ? reportData.chartData.values[index].toLocaleString()
                              : reportData.chartData.values[index]
                            }
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
                        📊 Sample Power BI Chart Data
                      </div>
                    )}
                  </div>
                </div>

                  <div className="mock-kpi-section">
                    <div className="kpi-card">
                      <div className="kpi-title">Total Records</div>
                      <div className="kpi-value">{reportData.metadata?.dataPoints || 0}</div>
                    </div>
                    <div className="kpi-card">
                      <div className="kpi-title">Generated</div>
                      <div className="kpi-value">
                        {new Date(reportData.metadata?.generatedAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="kpi-card">
                      <div className="kpi-title">Status</div>
                      <div className="kpi-value status-active">Active</div>
                    </div>
                  </div>

                {/* Simple Footer */}
                <div style={{
                  borderTop: '1px solid #e1e5e9',
                  paddingTop: '15px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '16px' }}>🔷</span>
                    <span>Powered by Microsoft Power BI</span>
                  </div>
                  <div>
                    Report ID: {reportData.reportId?.substring(0, 8)}...
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer Info */}
      {!isLoading && !error && (
        <div className="powerbi-footer">
          <div className="report-metadata">
            <span className="metadata-item">
              📊 {reportData.metadata?.reportType || 'Interactive Dashboard'}
            </span>
            {reportData.expiresAt && (
              <span className="metadata-item">
                ⏰ Expires: {new Date(reportData.expiresAt).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PowerBIEmbed;
