# 🚀 Setup Guide

## Quick Setup (5 Minutes)

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Database
Create `.env` file:
```bash
# Database Configuration
INVOICING_DB_HOST=your-server.database.windows.net
INVOICING_DB_PORT=1433
INVOICING_DB_NAME=YourDatabase
INVOICING_DB_USER=readonly_user
INVOICING_DB_PASSWORD=YourPassword123!

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Update Database Schema
Edit `config/database_config.yaml`:
```yaml
data_sources:
  invoicing_db:
    auto_discovery:
      include_schemas: ["eInvoicing"]  # Your actual schemas
```

### 4. Implement Authentication
Update `src/report_manager/database/security.py`:
```python
def _authenticate_against_database(self, username: str, password: str):
    # Replace with your authentication logic
    # Query your user database
    # Verify credentials
    # Return user info with role
```

### 5. Start API Server
```bash
python -m uvicorn src.report_manager.api.server:app --reload
```

### 6. Test
```bash
curl -X POST http://localhost:8000/query/excel \
  -H "Content-Type: application/json" \
  -d '{"text": "I need all einvoicing Tax code"}'
```

## Frontend Integration

```javascript
// Always use /query/excel endpoint
const response = await fetch('/query/excel', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: JSON.stringify({
    text: userInput  // Any natural language query
  })
});

const excelData = await response.json();
// Generate Excel file from excelData
```

## Production Checklist

- [ ] Configure actual database credentials
- [ ] Implement real authentication system
- [ ] Set up proper logging
- [ ] Configure SSL/HTTPS
- [ ] Set up monitoring
- [ ] Test with production data

## Support

For issues or questions, check the main README.md file.
