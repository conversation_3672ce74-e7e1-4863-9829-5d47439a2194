# 🔐 Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the Report Manager application.

## 📋 Prerequisites

- A Google account
- Access to Google Cloud Console

## 🚀 Step-by-Step Setup

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: `report-manager-oauth`
4. Click "Create"

### 2. Enable Google Identity Services

1. In the Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for "Google Identity Services API"
3. Click on it and press "Enable"

### 3. Configure OAuth Consent Screen

1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace)
3. Fill in the required information:
   - **App name**: Report Manager
   - **User support email**: Your email
   - **Developer contact email**: Your email
4. Add scopes:
   - `openid`
   - `email`
   - `profile`
5. Add test users (your email addresses)
6. Save and continue

### 4. Create OAuth Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. Choose "Web application"
4. Configure:
   - **Name**: Report Manager Web Client
   - **Authorized JavaScript origins**: 
     - `http://localhost:3001` (for development)
     - Your production domain (when deployed)
   - **Authorized redirect URIs**:
     - `http://localhost:3001` (for development)
     - Your production domain (when deployed)
5. Click "Create"
6. **Copy the Client ID** - you'll need this!

### 5. Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and replace the placeholder with your actual Client ID:
   ```env
   VITE_GOOGLE_CLIENT_ID=your-actual-client-id.apps.googleusercontent.com
   VITE_REDIRECT_URI=http://localhost:3001
   ```

3. Restart the development server:
   ```bash
   npm run dev
   ```

## 🧪 Testing

1. Open the application in your browser
2. You should see the login screen with "Continue with Google" button
3. Click the button to test Google OAuth
4. Sign in with your Google account
5. Grant permissions to the application
6. You should be redirected back to the app and see your profile

## 🔧 Demo Mode

The application includes a demo mode that works without real Google OAuth credentials:

- **Demo mode is enabled** when `VITE_GOOGLE_CLIENT_ID` is not set or equals `demo-mode`
- **Demo user**: Demo User (<EMAIL>)
- **Features**: All app features work normally, just with a fake user

To disable demo mode, set a real Google Client ID in your `.env` file.

## 🚀 Production Deployment

When deploying to production:

1. Update your Google OAuth configuration:
   - Add your production domain to authorized origins
   - Add your production domain to redirect URIs

2. Update environment variables:
   ```env
   VITE_GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
   VITE_REDIRECT_URI=https://your-domain.com
   ```

3. Ensure your OAuth consent screen is published (not in testing mode)

## 🔒 Security Notes

- Never commit your `.env` file to version control
- Keep your Client ID secure (though it's not a secret, it identifies your app)
- Regularly review OAuth scopes and permissions
- Monitor OAuth usage in Google Cloud Console

## 🆘 Troubleshooting

### "Error 400: redirect_uri_mismatch"
- Check that your redirect URI in Google Console matches exactly
- Ensure you're using the correct port (3001 for development)

### "Error 403: access_denied"
- Check that your OAuth consent screen is configured
- Ensure the user is added as a test user (if in testing mode)

### "This app isn't verified"
- This is normal for apps in testing mode
- Click "Advanced" → "Go to Report Manager (unsafe)" to continue
- For production, submit your app for verification

## 📞 Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Google Cloud Console configuration
3. Ensure environment variables are set correctly
4. Try demo mode first to isolate OAuth issues
