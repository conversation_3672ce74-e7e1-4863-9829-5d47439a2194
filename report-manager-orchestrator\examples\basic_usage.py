#!/usr/bin/env python3
"""
Basic Usage Example for Report Manager Orchestrator

This example shows how to use the orchestrator programmatically.
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from report_manager.core.orchestrator import ReportManagerOrchestrator
from report_manager.core.flow import FlowController
from report_manager.utils.config import config


async def basic_example():
    """Basic usage example"""
    print("🎯 Report Manager Orchestrator - Basic Usage Example")
    print("=" * 60)

    # Check configuration
    if not config.validate():
        print("❌ Configuration validation failed:")
        for error in config.get_validation_errors():
            print(f"   - {error}")
        return

    # Initialize orchestrator
    if config.should_use_azure_openai():
        azure_config = config.get_azure_openai_config()
        orchestrator = ReportManagerOrchestrator(
            openai_api_key=azure_config["api_key"],
            use_azure=True,
            azure_endpoint=f"https://{azure_config['endpoint']}.openai.azure.com/",
            azure_deployment=azure_config["deployment"],
            azure_api_version=azure_config["api_version"],
            temperature=config.get("openai.temperature", 0.1)
        )
        print("✅ Using Azure OpenAI")
    else:
        openai_config = config.get_openai_config()
        orchestrator = ReportManagerOrchestrator(
            openai_api_key=openai_config["api_key"],
            model_name=openai_config["model"],
            temperature=openai_config["temperature"]
        )
        print("✅ Using OpenAI")

    # Initialize flow controller
    flow_controller = FlowController(orchestrator)

    # Example queries
    queries = [
        "Show me all reports",
        "Find completed sales reports",
        "Analyze customer metrics trends",
        "Count reports by status"
    ]

    print("\n📝 Processing Example Queries:")
    print("-" * 40)

    for i, query in enumerate(queries, 1):
        print(f"\n{i}. Query: {query}")

        try:
            # Process the query
            result = await flow_controller.execute_simple_flow(query)

            if result.success:
                print(f"   ✅ Success")
                print(f"   Intent: {result.context.intent}")
                print(f"   Task Type: {result.context.task_type.value}")
                print(f"   Agents Used: {result.agents_used}")
                print(f"   Execution Time: {result.execution_time:.3f}s")

                # Show result summary
                if result.result and isinstance(result.result, dict):
                    print(f"   Result Keys: {list(result.result.keys())}")
            else:
                print(f"   ❌ Failed: {result.error}")

        except Exception as e:
            print(f"   ❌ Error: {e}")

    print("\n" + "=" * 60)
    print("✅ Basic usage example completed!")


async def context_creation_example():
    """Example of context creation"""
    print("\n🧠 Context Creation Example:")
    print("-" * 30)

    # Initialize orchestrator (simplified for example)
    orchestrator = ReportManagerOrchestrator(
        openai_api_key="test_key",  # This would be your real key
        model_name="gpt-4",
        temperature=0.1
    )

    # Example user inputs
    user_inputs = [
        "Show me the latest financial reports",
        "I need to analyze customer satisfaction trends",
        "Generate a summary of Q1 sales performance"
    ]

    for user_input in user_inputs:
        print(f"\nInput: {user_input}")

        try:
            # Create context (this would normally call the LLM)
            # For this example, we'll show what the context would look like
            print("   Context would include:")
            print("   - Intent: extracted from user input")
            print("   - Task Type: determined by analysis")
            print("   - Entities: key terms identified")
            print("   - Confidence: how sure the system is")

        except Exception as e:
            print(f"   Error: {e}")


def main():
    """Main function"""
    try:
        # Run basic example
        asyncio.run(basic_example())

        # Run context creation example
        asyncio.run(context_creation_example())

    except KeyboardInterrupt:
        print("\n👋 Example interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running example: {e}")


if __name__ == "__main__":
    main()