/**
 * Enhanced Excel Generator with Visual Chart Representations
 */
import ExcelJS from 'exceljs';

class EnhancedExcelGenerator {
  /**
   * Generate Excel file with visual chart representations
   * @param {Array} data - Array of objects to convert to Excel
   * @param {string} filename - Desired filename
   * @param {Object} chartData - Chart data to visualize
   * @returns {Object} Download info with blob URL
   */
  static async generateWithVisualChart(data, filename = 'report.xlsx', chartData = null) {
    try {
      console.log('📊 Generating Excel with visual chart representations...');
      
      const workbook = new ExcelJS.Workbook();
      workbook.creator = 'Report Manager';
      workbook.created = new Date();
      
      // 1. DATA SHEET - Raw data with professional formatting
      const dataSheet = workbook.addWorksheet('📊 Data');
      
      if (data && data.length > 0) {
        const headers = Object.keys(data[0]);
        
        // Add title
        dataSheet.mergeCells('A1:' + String.fromCharCode(64 + headers.length) + '1');
        const titleCell = dataSheet.getCell('A1');
        titleCell.value = 'Employee Salary Data';
        titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFF' } };
        titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '2E86AB' } };
        titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
        dataSheet.getRow(1).height = 25;
        
        // Add headers
        const headerRow = dataSheet.addRow(headers);
        headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
        headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '366092' } };
        
        // Add data
        data.forEach(row => {
          const values = headers.map(header => row[header]);
          dataSheet.addRow(values);
        });
        
        // Auto-fit columns
        dataSheet.columns.forEach(column => {
          column.width = 18;
        });
        
        // Add borders
        const range = dataSheet.getCell('A2').address + ':' + 
                     dataSheet.getCell(String.fromCharCode(64 + headers.length) + (data.length + 2)).address;
        dataSheet.getCell(range).border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      }
      
      // 2. VISUAL CHART SHEET - ASCII art and visual representation
      if (chartData && chartData.data) {
        const chartSheet = workbook.addWorksheet('🎨 Visual Chart');
        
        // Chart title
        chartSheet.mergeCells('A1:H1');
        const chartTitleCell = chartSheet.getCell('A1');
        chartTitleCell.value = chartData.title || 'Chart Visualization';
        chartTitleCell.font = { bold: true, size: 20, color: { argb: 'FFFFFF' } };
        chartTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '2E86AB' } };
        chartTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
        chartSheet.getRow(1).height = 35;
        
        chartSheet.addRow([]); // Empty row
        
        // Headers
        const headerRow = chartSheet.addRow(['Category', 'Value', 'Percentage', 'Visual Bar (30 chars)', 'Rank', 'Color Code']);
        headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
        headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '366092' } };
        
        const total = chartData.data.values.reduce((a, b) => a + b, 0);
        const sortedData = chartData.data.labels.map((label, index) => ({
          label,
          value: chartData.data.values[index],
          percentage: ((chartData.data.values[index] / total) * 100).toFixed(1)
        })).sort((a, b) => b.value - a.value);
        
        // Color palette
        const colors = ['FF6384', '36A2EB', 'FFCE56', '4BC0C0', '9966FF', 'FF9F40'];
        
        // Add data rows with visual bars
        sortedData.forEach((item, index) => {
          const barLength = Math.round((item.value / total) * 30);
          const visualBar = '█'.repeat(barLength) + '░'.repeat(30 - barLength);
          
          const row = chartSheet.addRow([
            item.label,
            item.value.toLocaleString(),
            `${item.percentage}%`,
            visualBar,
            `#${index + 1}`,
            `Color ${index + 1}`
          ]);
          
          // Apply color coding
          const colorCode = colors[index % colors.length];
          row.getCell(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colorCode + '40' } };
          row.getCell(6).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: colorCode } };
          
          // Highlight top and bottom
          if (index === 0) {
            row.getCell(5).value = '👑 #1';
            row.getCell(5).font = { bold: true };
          } else if (index === sortedData.length - 1) {
            row.getCell(5).value = '📉 Last';
          }
          
          // Style visual bar
          row.getCell(4).font = { name: 'Courier New', size: 10 };
        });
        
        // Set column widths
        chartSheet.getColumn(1).width = 20;
        chartSheet.getColumn(2).width = 15;
        chartSheet.getColumn(3).width = 12;
        chartSheet.getColumn(4).width = 35;
        chartSheet.getColumn(5).width = 12;
        chartSheet.getColumn(6).width = 15;
        
        // Add ASCII donut chart representation
        const asciiStartRow = sortedData.length + 7;
        chartSheet.mergeCells(`A${asciiStartRow}:H${asciiStartRow}`);
        const asciiTitleCell = chartSheet.getCell(`A${asciiStartRow}`);
        asciiTitleCell.value = '🍩 ASCII DONUT CHART REPRESENTATION';
        asciiTitleCell.font = { bold: true, size: 14, color: { argb: 'FFFFFF' } };
        asciiTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '2E86AB' } };
        asciiTitleCell.alignment = { horizontal: 'center' };
        
        // Create ASCII donut chart
        const asciiChart = this.createASCIIDonutChart(sortedData);
        asciiChart.forEach((line, index) => {
          const row = chartSheet.addRow([line, '', '', '', '', '']);
          chartSheet.mergeCells(`A${row.number}:H${row.number}`);
          row.getCell(1).font = { name: 'Courier New', size: 8 };
          row.getCell(1).alignment = { horizontal: 'center' };
        });
      }
      
      // 3. INSTRUCTIONS SHEET
      const instructionSheet = workbook.addWorksheet('📋 Instructions');
      
      // Title
      instructionSheet.mergeCells('A1:D1');
      const instTitleCell = instructionSheet.getCell('A1');
      instTitleCell.value = '📋 HOW TO CREATE CHARTS IN EXCEL';
      instTitleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFF' } };
      instTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '2E86AB' } };
      instTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
      instructionSheet.getRow(1).height = 25;
      
      const instructions = [
        '',
        '🎯 STEP-BY-STEP CHART CREATION:',
        '',
        '1. Go to the "📊 Data" sheet',
        '2. Select the data range (Department and Total_Salary columns)',
        '3. Click Insert → Charts → Doughnut Chart (for donut charts)',
        '4. Or Insert → Charts → Pie Chart (for pie charts)',
        '5. Excel will create an interactive chart',
        '',
        '🎨 VISUAL REPRESENTATION:',
        '',
        '• The "🎨 Visual Chart" sheet shows data with visual bars',
        '• Each bar represents the proportion of that category',
        '• Colors match the chart color scheme',
        '• ASCII art provides a text-based chart view',
        '',
        '📊 CHART FEATURES:',
        '',
        '• Interactive tooltips when you hover',
        '• Percentage calculations included',
        '• Data sorted by value (highest to lowest)',
        '• Color-coded for easy identification',
        '',
        '💡 TIPS:',
        '',
        '• Right-click on chart to customize colors',
        '• Add data labels for better readability',
        '• Use chart title from the data provided',
        '• Export chart as image if needed'
      ];
      
      instructions.forEach(instruction => {
        const row = instructionSheet.addRow([instruction, '', '', '']);
        if (instruction.includes('🎯') || instruction.includes('🎨') || instruction.includes('📊') || instruction.includes('💡')) {
          row.getCell(1).font = { bold: true, size: 12 };
        }
        instructionSheet.mergeCells(`A${row.number}:D${row.number}`);
      });
      
      instructionSheet.getColumn(1).width = 60;
      
      // Generate Excel buffer
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      const downloadUrl = URL.createObjectURL(blob);
      
      console.log('✅ Enhanced Excel with visual charts generated successfully');
      
      return {
        downloadUrl,
        filename,
        size: blob.size,
        timestamp: new Date().toISOString(),
        type: 'excel',
        hasChartData: !!chartData,
        hasVisualChart: true,
        hasASCIIChart: true
      };
      
    } catch (error) {
      console.error('❌ Error generating enhanced Excel:', error);
      throw error;
    }
  }
  
  /**
   * Create ASCII donut chart representation
   * @param {Array} data - Sorted data array
   * @returns {Array} Array of ASCII art lines
   */
  static createASCIIDonutChart(data) {
    const lines = [];
    
    lines.push('        ████████████████████████');
    lines.push('    ████                    ████');
    lines.push('  ██                            ██');
    lines.push(' ██                              ██');
    lines.push('██                                ██');
    lines.push('██                                ██');
    lines.push('██           DONUT CHART          ██');
    lines.push('██                                ██');
    lines.push('██                                ██');
    lines.push(' ██                              ██');
    lines.push('  ██                            ██');
    lines.push('    ████                    ████');
    lines.push('        ████████████████████████');
    lines.push('');
    lines.push('📊 LEGEND:');
    
    data.forEach((item, index) => {
      const symbol = ['█', '▓', '▒', '░', '▪', '▫'][index % 6];
      lines.push(`${symbol} ${item.label}: ${item.percentage}% (${item.value.toLocaleString()})`);
    });
    
    return lines;
  }
}

export default EnhancedExcelGenerator;
