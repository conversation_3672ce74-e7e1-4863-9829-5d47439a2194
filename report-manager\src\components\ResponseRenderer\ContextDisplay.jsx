import React, { useState } from 'react';
import './ContextDisplay.css';

const ContextDisplay = ({ context }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!context || typeof context !== 'object') {
    return null;
  }

  const { intent, task_type, entities, confidence, parameters } = context;

  const getConfidenceColor = (conf) => {
    if (conf >= 0.8) return 'confidence-high';
    if (conf >= 0.6) return 'confidence-medium';
    return 'confidence-low';
  };

  const formatConfidence = (conf) => {
    return `${(conf * 100).toFixed(0)}%`;
  };

  return (
    <div className="context-display">
      <div 
        className="context-header"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className="context-title">🎯 Query Context</span>
        <span className="expand-icon">{isExpanded ? '▼' : '▶'}</span>
      </div>
      
      {isExpanded && (
        <div className="context-content">
          {intent && (
            <div className="context-item">
              <span className="context-label">Intent:</span>
              <span className="context-value">{intent}</span>
            </div>
          )}
          
          {task_type && (
            <div className="context-item">
              <span className="context-label">Task Type:</span>
              <span className="context-value">{task_type}</span>
            </div>
          )}
          
          {confidence !== undefined && (
            <div className="context-item">
              <span className="context-label">Confidence:</span>
              <span className={`context-value confidence ${getConfidenceColor(confidence)}`}>
                {formatConfidence(confidence)}
              </span>
            </div>
          )}
          
          {entities && Array.isArray(entities) && entities.length > 0 && (
            <div className="context-item">
              <span className="context-label">Entities:</span>
              <div className="entities-list">
                {entities.map((entity, index) => (
                  <span key={index} className="entity-tag">
                    {typeof entity === 'object' ? entity.value || entity.text : entity}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {parameters && typeof parameters === 'object' && Object.keys(parameters).length > 0 && (
            <div className="context-item">
              <span className="context-label">Parameters:</span>
              <div className="parameters-list">
                {Object.entries(parameters).map(([key, value]) => (
                  <div key={key} className="parameter-item">
                    <span className="param-key">{key}:</span>
                    <span className="param-value">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ContextDisplay;
