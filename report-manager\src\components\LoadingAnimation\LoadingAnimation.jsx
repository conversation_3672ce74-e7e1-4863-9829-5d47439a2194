import React, { useState, useEffect } from 'react';
import './LoadingAnimation.css';

const LoadingAnimation = ({ 
  messages = [
    "🔍 Analyzing your request...",
    "📊 Processing data...",
    "🎨 Generating charts...",
    "📋 Creating Excel report...",
    "✨ Finalizing your report..."
  ],
  duration = 3000,
  onComplete = null
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const messageInterval = setInterval(() => {
      setCurrentMessageIndex(prev => {
        if (prev < messages.length - 1) {
          return prev + 1;
        } else {
          // Animation complete
          setTimeout(() => {
            setIsVisible(false);
            if (onComplete) {
              onComplete();
            }
          }, 1000);
          return prev;
        }
      });
    }, duration / messages.length);

    return () => clearInterval(messageInterval);
  }, [messages, duration, onComplete]);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="loading-animation">
      <div className="loading-container">
        <div className="loading-spinner">
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>
        
        <div className="loading-messages">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`loading-message ${
                index === currentMessageIndex ? 'active' : 
                index < currentMessageIndex ? 'completed' : 'pending'
              }`}
            >
              {index < currentMessageIndex && <span className="check-mark">✓</span>}
              {index === currentMessageIndex && <span className="loading-dots">⋯</span>}
              <span className="message-text">{message}</span>
            </div>
          ))}
        </div>
        
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ 
              width: `${((currentMessageIndex + 1) / messages.length) * 100}%` 
            }}
          ></div>
        </div>
        
        <div className="loading-subtitle">
          Please wait while we prepare your report...
        </div>
      </div>
    </div>
  );
};

export default LoadingAnimation;
