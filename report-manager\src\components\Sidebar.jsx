import { useState, useEffect } from 'react';
import '../assets/Style/Sidebar.css';

export default function Sidebar({ conversations, activeConversation, onNewChat, onSelectConversation, user, onSignOut }) {
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-icon')) {
        setShowUserDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleSignOut = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      setIsSigningOut(true);
      console.log('Starting sign out process...');

      if (onSignOut) {
        await onSignOut();
      }

      console.log('Sign out completed');
    } catch (error) {
      console.error('Sign-out failed:', error);
    } finally {
      setIsSigningOut(false);
      setShowUserDropdown(false);
    }
  };

  const toggleUserDropdown = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Toggling user dropdown, current state:', showUserDropdown);
    setShowUserDropdown(!showUserDropdown);
  };
  return (
    <div className="sidebar">
      <div className="sidebar-top">
        <div className="sidebar-icon logo">
          <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" strokeWidth="2.5"/>
          </svg>
        </div>

        <div className="sidebar-icon" onClick={onNewChat}>
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <path d="M12 5v14m-7-7h14" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>

        <div className="sidebar-icon active">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span className="sidebar-label">Home</span>
        </div>

        <div className="sidebar-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
            <line x1="2" y1="12" x2="22" y2="12" stroke="currentColor" strokeWidth="2"/>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span className="sidebar-label">Discover</span>
        </div>

        <div className="sidebar-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span className="sidebar-label">Spaces</span>
        </div>
      </div>
      
      <div className="sidebar-bottom">
        {/* Use existing Account icon with dropdown functionality */}
        {user && (
          <div className="sidebar-icon user-icon" onClick={toggleUserDropdown}>
            {user.picture ? (
              <img
                src={user.picture}
                alt={user.name || user.email}
                className="user-avatar-img"
              />
            ) : (
              <div className="user-avatar">
                {(user.name || user.email || 'U').charAt(0).toUpperCase()}
              </div>
            )}
            <span className="sidebar-label">Account</span>

            {/* User Dropdown - positioned to the right */}
            {showUserDropdown && (
              <div className="user-dropdown">
                <div className="dropdown-header">
                  <div className="dropdown-user-info">
                    <div className="dropdown-user-name">
                      {user.name || user.email?.split('@')[0] || 'User'}
                    </div>
                    <div className="dropdown-user-email">
                      {user.email}
                    </div>
                  </div>
                </div>

                <div className="dropdown-divider"></div>

                <button
                  className={`dropdown-item logout-item ${isSigningOut ? 'loading' : ''}`}
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  type="button"
                >
                  {isSigningOut ? (
                    <>
                      <div className="logout-spinner"></div>
                      <span>Signing out...</span>
                    </>
                  ) : (
                    <>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <polyline points="16,17 21,12 16,7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span>Sign Out</span>
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        )}

        <div className="sidebar-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2"/>
          </svg>
          <span className="sidebar-label">Upgrade</span>
        </div>

      </div>
    </div>
  );
}


