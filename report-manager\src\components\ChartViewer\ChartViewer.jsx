import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import DonutChart from '../Charts/DonutChart';
import BarChart from '../Charts/BarChart';
import ChartLinkGenerator from '../../utils/chartLinkGenerator';
import './ChartViewer.css';

const ChartViewer = () => {
  const { chartId } = useParams();
  const [searchParams] = useSearchParams();
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadChartData = async () => {
      try {
        setLoading(true);
        
        // Try to get data from URL parameter first
        const dataParam = searchParams.get('data');
        if (dataParam) {
          console.log('📊 Loading chart data from URL parameter...');
          const decodedData = JSON.parse(atob(dataParam));
          setChartData(decodedData);
          
          // Store for future access
          ChartLinkGenerator.storeChartData(chartId, decodedData);
        } else {
          // Try to retrieve from storage
          console.log('📊 Loading chart data from storage...');
          const storedData = ChartLinkGenerator.retrieveChartData(chartId);
          if (storedData) {
            setChartData(storedData);
          } else {
            setError('Chart not found or has expired');
          }
        }
        
      } catch (err) {
        console.error('❌ Error loading chart data:', err);
        setError('Failed to load chart data');
      } finally {
        setLoading(false);
      }
    };

    if (chartId) {
      loadChartData();
    }
  }, [chartId, searchParams]);

  const renderChart = () => {
    if (!chartData || !chartData.data) return null;

    const { type, data, title, options = {} } = chartData;

    switch (type?.toLowerCase()) {
      case 'donut':
      case 'doughnut':
      case 'pie':
        return (
          <DonutChart
            data={data}
            title={title}
            width={options.width || 500}
            height={options.height || 500}
            showLegend={options.showLegend !== false}
            showTooltips={options.showTooltips !== false}
            colors={options.colors}
          />
        );
      
      case 'bar':
      case 'column':
        return (
          <BarChart
            data={data}
            title={title}
            width={options.width || 500}
            height={options.height || 400}
            showLegend={options.showLegend !== false}
            showTooltips={options.showTooltips !== false}
            colors={options.colors}
          />
        );
      
      default:
        return (
          <div className="chart-error">
            <p>Unsupported chart type: {type}</p>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="chart-viewer-container">
        <div className="chart-viewer-loading">
          <div className="loading-spinner"></div>
          <h2>Loading Chart...</h2>
          <p>Please wait while we load your chart data</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="chart-viewer-container">
        <div className="chart-viewer-error">
          <h2>❌ Chart Not Found</h2>
          <p>{error}</p>
          <div className="error-suggestions">
            <h3>Possible reasons:</h3>
            <ul>
              <li>The chart link has expired (charts expire after 30 days)</li>
              <li>The chart ID is invalid or corrupted</li>
              <li>The chart data was not properly generated</li>
            </ul>
            <button 
              onClick={() => window.location.href = '/'}
              className="back-home-btn"
            >
              🏠 Go Back to Report Manager
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!chartData) {
    return (
      <div className="chart-viewer-container">
        <div className="chart-viewer-error">
          <h2>No Chart Data</h2>
          <p>No chart data available to display</p>
        </div>
      </div>
    );
  }

  return (
    <div className="chart-viewer-container">
      <div className="chart-viewer-header">
        <h1>📊 {chartData.title || 'Chart Viewer'}</h1>
        <div className="chart-info">
          <span className="chart-type">Type: {chartData.type?.toUpperCase()}</span>
          <span className="chart-generated">
            Generated: {new Date(chartData.timestamp).toLocaleString()}
          </span>
          <span className="chart-expires">
            Expires: {new Date(chartData.generated_at || Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleString()}
          </span>
        </div>
      </div>

      <div className="chart-viewer-content">
        <div className="chart-display">
          {renderChart()}
        </div>

        <div className="chart-metadata">
          <h3>📋 Chart Information</h3>
          <div className="metadata-grid">
            <div className="metadata-item">
              <strong>Data Points:</strong>
              <span>{chartData.data?.labels?.length || 0}</span>
            </div>
            <div className="metadata-item">
              <strong>Total Value:</strong>
              <span>{chartData.data?.values?.reduce((a, b) => a + b, 0)?.toLocaleString() || 'N/A'}</span>
            </div>
            <div className="metadata-item">
              <strong>Categories:</strong>
              <span>{chartData.data?.labels?.join(', ') || 'N/A'}</span>
            </div>
            <div className="metadata-item">
              <strong>Generated By:</strong>
              <span>{chartData.generated_by || 'Report Manager'}</span>
            </div>
          </div>
        </div>

        <div className="chart-actions">
          <button 
            onClick={() => window.print()}
            className="action-btn print-btn"
          >
            🖨️ Print Chart
          </button>
          <button 
            onClick={() => {
              const url = window.location.href;
              navigator.clipboard.writeText(url);
              alert('Chart link copied to clipboard!');
            }}
            className="action-btn share-btn"
          >
            🔗 Copy Link
          </button>
          <button 
            onClick={() => window.location.href = '/'}
            className="action-btn home-btn"
          >
            🏠 Back to Report Manager
          </button>
        </div>

        {chartData.data?.summary && (
          <div className="chart-summary">
            <h3>📊 Summary Statistics</h3>
            <div className="summary-grid">
              {chartData.data.summary.map((item, index) => (
                <div key={index} className="summary-item">
                  <strong>{item.label}:</strong>
                  <span>{item.value}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="chart-viewer-footer">
        <p>
          This chart was generated by Report Manager. 
          Links expire after 30 days for security purposes.
        </p>
      </div>
    </div>
  );
};

export default ChartViewer;
