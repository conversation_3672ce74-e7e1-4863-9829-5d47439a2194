import React, { useState } from 'react';
import './DownloadRenderer.css';

const DownloadRenderer = ({ downloadInfo, onDownload, chartContext = null }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadComplete, setDownloadComplete] = useState(false);

  console.log('DownloadRenderer received:', downloadInfo);
  console.log('Chart context:', chartContext);

  if (!downloadInfo) {
    console.log('No downloadInfo provided');
    return null;
  }

  if (!downloadInfo.downloadUrl) {
    console.log('No downloadUrl in downloadInfo:', downloadInfo);
    return (
      <div className="download-renderer">
        <div className="download-error">
          <div className="error-icon">⚠️</div>
          <div className="error-content">
            <div className="error-title">Download Not Available</div>
            <div className="error-message">Unable to generate download link. Please try again.</div>
          </div>
        </div>
      </div>
    );
  }

  const { downloadUrl, filename, size, format, timestamp } = downloadInfo;

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFormatIcon = (format) => {
    switch (format?.toLowerCase()) {
      case 'excel':
      case 'xlsx':
        return '📊';
      case 'pdf':
        return '📄';
      case 'csv':
        return '📋';
      case 'word':
      case 'docx':
        return '📝';
      default:
        return '📁';
    }
  };

  const getFormatColor = (format) => {
    switch (format?.toLowerCase()) {
      case 'excel':
      case 'xlsx':
        return 'format-excel';
      case 'pdf':
        return 'format-pdf';
      case 'csv':
        return 'format-csv';
      case 'word':
      case 'docx':
        return 'format-word';
      default:
        return 'format-default';
    }
  };

  const handleDownload = async () => {
    if (isDownloading || downloadComplete) return;

    setIsDownloading(true);
    try {
      console.log('Starting download:', { downloadUrl, filename, size });

      // Create download link
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'report.xlsx';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Call the onDownload callback if provided
      if (onDownload) {
        onDownload(downloadInfo);
      }

      // Show success state after a brief delay
      setTimeout(() => {
        setDownloadComplete(true);
      }, 1000);

      console.log('Download initiated for:', filename);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const getFileTypeLabel = (filename) => {
    if (!filename) return 'FILE';

    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'xlsx':
      case 'xls':
        return 'EXCEL';
      case 'pdf':
        return 'PDF';
      case 'csv':
        return 'CSV';
      case 'doc':
      case 'docx':
        return 'WORD';
      default:
        return extension?.toUpperCase() || 'FILE';
    }
  };

  return (
    <div className="download-renderer">
      <div className="download-card">
        <div className="file-icon">
          <span className={`icon ${getFormatColor(format)}`}>
            {getFormatIcon(format)}
          </span>
        </div>

        <div className="file-details">
          <div className="file-name">{filename}</div>
          <div className="file-meta">
            <span className="file-size">{formatFileSize(size)}</span>
            <span className="file-type">{getFileTypeLabel(filename)}</span>
          </div>
          {chartContext && (
            <div className="chart-context">
              <span className="chart-indicator">📊</span>
              <span className="chart-text">{chartContext}</span>
            </div>
          )}
        </div>

        <button
          className={`download-btn ${isDownloading ? 'downloading' : ''} ${downloadComplete ? 'completed' : ''}`}
          onClick={handleDownload}
          disabled={isDownloading}
          title="Download report"
        >
          {downloadComplete ? (
            <span className="btn-icon">✅</span>
          ) : isDownloading ? (
            <span className="btn-icon loading">⏳</span>
          ) : (
            <span className="btn-icon">⬇️</span>
          )}
        </button>
      </div>
    </div>
  );
};

export default DownloadRenderer;
