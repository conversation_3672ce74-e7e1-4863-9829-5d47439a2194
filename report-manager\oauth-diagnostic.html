<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Diagnostic Tool</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔐 OAuth Diagnostic Tool</h1>
    
    <div class="test-section">
        <h2>1. Environment Check</h2>
        <div id="env-status">Checking...</div>
    </div>

    <div class="test-section">
        <h2>2. Google Script Loading</h2>
        <div id="script-status">Checking...</div>
        <button onclick="loadGoogleScript()">Reload Google Script</button>
    </div>

    <div class="test-section">
        <h2>3. OAuth Configuration</h2>
        <div id="config-status">Checking...</div>
    </div>

    <div class="test-section">
        <h2>4. OAuth Test</h2>
        <div id="oauth-status">Ready to test</div>
        <button onclick="testOAuth()">Test OAuth Sign-In</button>
        <button onclick="testDirectAuth()">Test Direct Auth URL</button>
    </div>

    <div class="test-section">
        <h2>5. Console Logs</h2>
        <pre id="console-logs"></pre>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        const CLIENT_ID = '54548512375-lr3mkvm59r5nvofonf6otnm5ju6oa6tq.apps.googleusercontent.com';
        const REDIRECT_URI = 'http://localhost:3003';
        
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            console.log(logEntry);
            updateConsoleLogs();
        }
        
        function updateConsoleLogs() {
            document.getElementById('console-logs').textContent = logs.join('\n');
        }
        
        function clearLogs() {
            logs = [];
            updateConsoleLogs();
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 1. Environment Check
        function checkEnvironment() {
            log('Checking environment...');
            
            const checks = [
                { name: 'HTTPS/Localhost', check: location.protocol === 'http:' && location.hostname === 'localhost' },
                { name: 'Modern Browser', check: !!window.fetch && !!window.Promise },
                { name: 'Local Storage', check: !!window.localStorage },
                { name: 'Console Available', check: !!window.console }
            ];
            
            const results = checks.map(check => 
                `${check.check ? '✅' : '❌'} ${check.name}`
            ).join('<br>');
            
            const allPassed = checks.every(check => check.check);
            setStatus('env-status', results, allPassed ? 'success' : 'error');
            
            log(`Environment check: ${allPassed ? 'PASSED' : 'FAILED'}`);
        }
        
        // 2. Google Script Loading
        function loadGoogleScript() {
            log('Loading Google Identity Services script...');
            setStatus('script-status', 'Loading...', 'warning');
            
            // Remove existing script if any
            const existingScript = document.querySelector('script[src*="accounts.google.com"]');
            if (existingScript) {
                existingScript.remove();
                log('Removed existing Google script');
            }
            
            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.async = true;
            script.defer = true;
            
            script.onload = () => {
                log('Google script loaded successfully');
                setTimeout(() => {
                    if (window.google?.accounts?.id) {
                        setStatus('script-status', '✅ Google Identity Services loaded and available', 'success');
                        log('Google Identity Services API is available');
                        checkOAuthConfig();
                    } else {
                        setStatus('script-status', '❌ Google Identity Services not available after load', 'error');
                        log('Google Identity Services API not available');
                    }
                }, 500);
            };
            
            script.onerror = (error) => {
                setStatus('script-status', '❌ Failed to load Google script', 'error');
                log('Failed to load Google script: ' + error);
            };
            
            document.head.appendChild(script);
        }
        
        // 3. OAuth Configuration Check
        function checkOAuthConfig() {
            log('Checking OAuth configuration...');
            
            const config = {
                'Client ID': CLIENT_ID,
                'Redirect URI': REDIRECT_URI,
                'Current Origin': window.location.origin,
                'Google API Available': !!window.google?.accounts?.id
            };
            
            const configHtml = Object.entries(config).map(([key, value]) => 
                `<strong>${key}:</strong> ${value}`
            ).join('<br>');
            
            setStatus('config-status', configHtml, 'info');
            log('OAuth configuration checked');
        }
        
        // 4. OAuth Testing
        function testOAuth() {
            log('Testing OAuth sign-in...');
            setStatus('oauth-status', 'Testing...', 'warning');
            
            if (!window.google?.accounts?.id) {
                setStatus('oauth-status', '❌ Google Identity Services not available', 'error');
                log('Cannot test OAuth: Google Identity Services not available');
                return;
            }
            
            try {
                window.google.accounts.id.initialize({
                    client_id: CLIENT_ID,
                    callback: handleCredentialResponse,
                    auto_select: false,
                    cancel_on_tap_outside: true
                });
                
                window.google.accounts.id.prompt((notification) => {
                    log(`OAuth prompt notification: ${notification.getMomentType()}`);
                    
                    if (notification.isNotDisplayed()) {
                        setStatus('oauth-status', '⚠️ OAuth prompt not displayed. Check console for details.', 'warning');
                        log('OAuth prompt not displayed - possible configuration issue');
                    } else if (notification.isSkippedMoment()) {
                        setStatus('oauth-status', '⚠️ OAuth prompt skipped by user', 'warning');
                        log('OAuth prompt skipped');
                    }
                });
                
                log('OAuth test initiated');
            } catch (error) {
                setStatus('oauth-status', `❌ OAuth test failed: ${error.message}`, 'error');
                log('OAuth test error: ' + error.message);
            }
        }
        
        function handleCredentialResponse(response) {
            log('OAuth credential response received');
            setStatus('oauth-status', '✅ OAuth working! Credential received.', 'success');
            
            try {
                // Decode JWT to show user info
                const payload = JSON.parse(atob(response.credential.split('.')[1]));
                log(`OAuth success: ${payload.email} (${payload.name})`);
            } catch (e) {
                log('OAuth success but could not decode user info');
            }
        }
        
        function testDirectAuth() {
            log('Testing direct OAuth URL...');
            
            const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
                `client_id=${CLIENT_ID}&` +
                `redirect_uri=${encodeURIComponent(REDIRECT_URI)}&` +
                `response_type=token id_token&` +
                `scope=openid email profile&` +
                `nonce=test_${Date.now()}`;
            
            log('Opening OAuth URL in new window...');
            const popup = window.open(authUrl, 'oauth_test', 'width=500,height=600');
            
            if (!popup) {
                setStatus('oauth-status', '❌ Popup blocked. Please allow popups.', 'error');
                log('Popup blocked');
            } else {
                setStatus('oauth-status', '🔄 OAuth popup opened. Check popup window.', 'info');
                log('OAuth popup opened');
            }
        }
        
        // Initialize on page load
        window.onload = function() {
            log('OAuth Diagnostic Tool started');
            checkEnvironment();
            loadGoogleScript();
        };
    </script>
</body>
</html>
