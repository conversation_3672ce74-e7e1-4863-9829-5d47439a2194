.response-renderer {
  width: 100%;
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

.response-content {
  margin-bottom: 16px;
}

.response-actions {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}

.generate-report-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generate-report-btn:hover {
  background: #e0f2fe;
  border-color: #7dd3fc;
  transform: translateY(-1px);
}

.report-generator-section {
  margin: 16px 0;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.response-metadata {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
  margin-top: 12px;
}

.response-text {
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Chart-specific styles */
.response-renderer.chart-only {
  margin: 10px 0;
  padding: 0;
}

.chart-metadata {
  margin-top: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.metadata-text {
  color: #6c757d;
  font-size: 12px;
  font-style: italic;
}
