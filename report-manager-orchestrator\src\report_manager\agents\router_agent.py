"""
Router Agent for Report Manager Orchestrator

This agent determines which specialized agents should be called
based on the user context and task requirements.
"""

import asyncio
from typing import List, Dict, Any
from loguru import logger

from ..core.models import UserContext, TaskType


class RouterAgent:
    """
    Agent responsible for routing requests to appropriate specialized agents
    based on user context and task type.
    """

    def __init__(self):
        """Initialize the router agent"""
        self.agent_capabilities = {
            "query_agent": {
                "task_types": [TaskType.QUERY, TaskType.DATA_ANALYSIS],
                "capabilities": ["data_retrieval", "filtering", "aggregation", "search"],
                "priority": 1
            },
            "report_agent": {
                "task_types": [TaskType.REPORT_GENERATION],
                "capabilities": ["report_creation", "formatting", "visualization"],
                "priority": 2
            },
            "workflow_agent": {
                "task_types": [TaskType.WORKFLOW],
                "capabilities": ["process_automation", "task_scheduling", "coordination"],
                "priority": 3
            },
            "analysis_agent": {
                "task_types": [TaskType.DATA_ANALYSIS],
                "capabilities": ["statistical_analysis", "trend_analysis", "insights"],
                "priority": 2
            }
        }

        logger.info("Router agent initialized")

    async def route(self,
                   context: UserContext,
                   suggested_agents: List[str] = None) -> List[str]:
        """
        Determine which agents should handle the request

        Args:
            context: User context from orchestrator
            suggested_agents: Optional list of suggested agents from LLM

        Returns:
            List of agent names to use, ordered by priority
        """
        logger.info(f"Routing request with task type: {context.task_type}")

        # Start with agents that match the task type
        matching_agents = []

        for agent_name, capabilities in self.agent_capabilities.items():
            if context.task_type in capabilities["task_types"]:
                matching_agents.append({
                    "name": agent_name,
                    "priority": capabilities["priority"],
                    "capabilities": capabilities["capabilities"]
                })

        # Consider suggested agents from LLM
        if suggested_agents:
            for suggested in suggested_agents:
                if suggested in self.agent_capabilities:
                    # Add if not already in matching agents
                    if not any(agent["name"] == suggested for agent in matching_agents):
                        capabilities = self.agent_capabilities[suggested]
                        matching_agents.append({
                            "name": suggested,
                            "priority": capabilities["priority"] + 0.5,  # Slightly lower priority
                            "capabilities": capabilities["capabilities"]
                        })

        # Apply additional routing logic based on context
        filtered_agents = await self.apply_routing_rules(context, matching_agents)

        # Sort by priority and return agent names
        filtered_agents.sort(key=lambda x: x["priority"])
        agent_names = [agent["name"] for agent in filtered_agents]

        # Ensure we always have at least one agent
        if not agent_names:
            logger.warning("No matching agents found, defaulting to query_agent")
            agent_names = ["query_agent"]

        logger.info(f"Selected agents: {agent_names}")
        return agent_names

    async def apply_routing_rules(self,
                                context: UserContext,
                                candidate_agents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply additional routing rules based on context

        Args:
            context: User context
            candidate_agents: List of candidate agents with metadata

        Returns:
            Filtered list of agents
        """
        filtered_agents = []

        for agent in candidate_agents:
            should_include = True

            # Rule 1: For simple queries, prefer query_agent only
            if (context.task_type == TaskType.QUERY and
                len(context.entities) <= 2 and
                agent["name"] != "query_agent"):
                should_include = False

            # Rule 2: For complex analysis, include both query and analysis agents
            if (context.task_type == TaskType.DATA_ANALYSIS and
                "analysis" in context.intent.lower()):
                if agent["name"] in ["query_agent", "analysis_agent"]:
                    should_include = True

            # Rule 3: Check for specific keywords in intent
            intent_lower = context.intent.lower()

            if "report" in intent_lower and agent["name"] == "report_agent":
                should_include = True
            elif "workflow" in intent_lower and agent["name"] == "workflow_agent":
                should_include = True
            elif "query" in intent_lower and agent["name"] == "query_agent":
                should_include = True

            # Rule 4: Consider confidence level
            if context.confidence < 0.5:
                # For low confidence, prefer simpler agents
                if agent["name"] in ["query_agent"]:
                    should_include = True
                else:
                    should_include = False

            if should_include:
                filtered_agents.append(agent)

        return filtered_agents

    def get_agent_info(self, agent_name: str) -> Dict[str, Any]:
        """
        Get information about a specific agent

        Args:
            agent_name: Name of the agent

        Returns:
            Dictionary with agent information
        """
        return self.agent_capabilities.get(agent_name, {})

    def list_available_agents(self) -> List[str]:
        """
        Get list of all available agents

        Returns:
            List of agent names
        """
        return list(self.agent_capabilities.keys())

    async def validate_agent_chain(self, agents: List[str]) -> bool:
        """
        Validate that the selected agent chain makes sense

        Args:
            agents: List of agent names

        Returns:
            True if the chain is valid, False otherwise
        """
        # Basic validation rules

        # Rule 1: No duplicate agents
        if len(agents) != len(set(agents)):
            logger.warning("Duplicate agents in chain")
            return False

        # Rule 2: Query agent should come before analysis agent
        if "analysis_agent" in agents and "query_agent" in agents:
            query_idx = agents.index("query_agent")
            analysis_idx = agents.index("analysis_agent")
            if query_idx > analysis_idx:
                logger.warning("Query agent should come before analysis agent")
                return False

        # Rule 3: Maximum chain length
        if len(agents) > 3:
            logger.warning("Agent chain too long")
            return False

        return True