.sidebar {
  width: 120px;
  height: 100vh;
  background-color: #f8f9fa;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 24px 0;
  position: relative;
}

.sidebar-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.sidebar-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding-bottom: 24px;
}

.sidebar-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin-top: auto;
}

.sidebar-icon {
  width: 80px;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #4a5568;
  position: relative;
  padding: 12px 8px;
  min-height: 70px;
}

.sidebar-icon:hover {
  background-color: #edf2f7;
  color: #2d3748;
}

.sidebar-icon.active {
  background-color: #e6fffa;
  color: #319795;
  font-weight: 600;
}

.sidebar-icon.logo {
  margin-bottom: 16px;
  color: #2d3748;
  font-size: 28px;
  font-weight: 700;
  min-height: 60px;
}

.sidebar-label {
  font-size: 13px;
  font-weight: 500;
  margin-top: 8px;
  text-align: center;
  line-height: 1.3;
  color: #4a5568;
  letter-spacing: 0.025em;
  white-space: nowrap;
}

.sidebar-icon.active .sidebar-label {
  color: #319795;
  font-weight: 600;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #9f7aea 0%, #667eea 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  color: white;
  margin-bottom: 8px;
}

/* User Icon Styles */
.sidebar-icon.user-icon {
  position: relative;
  cursor: pointer;
}

.user-avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  left: 100%;
  bottom: 0;
  margin-left: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 220px;
  z-index: 1000;
  animation: dropdownSlideRight 0.2s ease-out;
}

@keyframes dropdownSlideRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.dropdown-header {
  padding: 16px;
}

.dropdown-user-info {
  text-align: left;
}

.dropdown-user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  word-break: break-word;
}

.dropdown-user-email {
  font-size: 14px;
  color: #6b7280;
  word-break: break-word;
}

.dropdown-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 0 12px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
  color: #374151;
}

.dropdown-item:hover {
  background: #f3f4f6;
}

.dropdown-item:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.logout-item {
  color: #dc2626 !important;
}

.logout-item:hover {
  background: #fef2f2 !important;
}

.logout-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #fecaca;
  border-top: 2px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

