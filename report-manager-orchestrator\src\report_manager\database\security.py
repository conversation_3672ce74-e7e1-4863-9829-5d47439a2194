"""
Database Security Module

This module provides comprehensive security features including:
- SQL injection prevention
- Query validation
- Access control
- Authentication and authorization
- Query sanitization
"""

import re
import hashlib
import secrets
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from loguru import logger

from .config_manager import DatabaseConfigManager


@dataclass
class SecurityViolation:
    """Represents a security violation"""
    violation_type: str
    description: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    query: str
    timestamp: datetime


@dataclass
class UserSession:
    """Represents a user session"""
    user_id: str
    role: str
    permissions: List[str]
    session_token: str
    created_at: datetime
    expires_at: datetime
    last_activity: datetime


class DatabaseSecurity:
    """
    Comprehensive database security manager
    """
    
    def __init__(self, config_manager: DatabaseConfigManager):
        """
        Initialize security manager
        
        Args:
            config_manager: Database configuration manager
        """
        self.config_manager = config_manager
        self.security_settings = config_manager.get_security_settings()
        self.access_control = config_manager.get_access_control()
        
        # Active user sessions
        self.active_sessions: Dict[str, UserSession] = {}
        
        # Security violation tracking
        self.violations: List[SecurityViolation] = []
        
        # SQL injection patterns (excluding SELECT for read-only queries)
        self.sql_injection_patterns = [
            r"(\b(union|insert|update|delete|drop|create|alter|exec|execute)\b)",
            r"(--|#|/\*|\*/)",
            r"(\b(or|and)\s+\d+\s*=\s*\d+)",
            r"(\b(or|and)\s+['\"].*['\"])",
            r"(;|\|\||&&)",
            r"(\bxp_cmdshell\b|\bsp_executesql\b)",
            r"(\b(script|javascript|vbscript)\b)",
            r"(<script|</script>|<iframe|</iframe>)"
        ]
        
        # Dangerous SQL keywords
        self.dangerous_keywords = [
            'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE',
            'EXEC', 'EXECUTE', 'XP_CMDSHELL', 'SP_EXECUTESQL', 'OPENROWSET',
            'BULK', 'SHUTDOWN', 'RESTORE', 'BACKUP'
        ]
        
        logger.info("Database security manager initialized")
    
    def authenticate_user(self, username: str, password: str) -> Optional[UserSession]:
        """
        Authenticate a user and create a session
        
        Args:
            username: Username
            password: Password
            
        Returns:
            User session if authentication successful, None otherwise
        """
        try:
            # TODO: Implement actual user authentication against your user database
            # This is a placeholder - replace with your authentication system
            user_info = self._authenticate_against_database(username, password)
            if not user_info:
                logger.warning(f"Authentication failed for user: {username}")
                return None
            
            # Create session
            session_token = secrets.token_urlsafe(32)
            now = datetime.now()
            
            # Get user permissions
            role = user_info['role']
            permissions = self._get_role_permissions(role)
            
            session = UserSession(
                user_id=username,
                role=role,
                permissions=permissions,
                session_token=session_token,
                created_at=now,
                expires_at=now + timedelta(hours=8),  # 8-hour session
                last_activity=now
            )
            
            self.active_sessions[session_token] = session
            
            logger.info(f"User authenticated successfully: {username} (role: {role})")
            return session
            
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return None

    def _authenticate_against_database(self, username: str, password: str) -> Optional[dict]:
        """
        Authenticate user against your database

        Replace this method with your actual authentication logic
        """
        # TODO: Implement your authentication logic here
        # Example:
        # - Query your user database
        # - Verify password hash
        # - Return user info with role

        # Placeholder implementation - REPLACE WITH YOUR LOGIC
        if username and password:
            return {
                'username': username,
                'role': 'readonly'  # Default role - determine from your database
            }
        return None
    
    def validate_session(self, session_token: str) -> Optional[UserSession]:
        """
        Validate a user session
        
        Args:
            session_token: Session token
            
        Returns:
            User session if valid, None otherwise
        """
        session = self.active_sessions.get(session_token)
        
        if not session:
            return None
        
        # Check if session expired
        if datetime.now() > session.expires_at:
            self.logout_user(session_token)
            return None
        
        # Update last activity
        session.last_activity = datetime.now()
        
        return session
    
    def logout_user(self, session_token: str) -> bool:
        """
        Logout a user and invalidate session
        
        Args:
            session_token: Session token
            
        Returns:
            True if logout successful
        """
        if session_token in self.active_sessions:
            user_id = self.active_sessions[session_token].user_id
            del self.active_sessions[session_token]
            logger.info(f"User logged out: {user_id}")
            return True
        
        return False
    
    def validate_query_security(self, query: str, user_session: UserSession, 
                              datasource: str, tables: List[str]) -> Tuple[bool, List[SecurityViolation]]:
        """
        Validate query security
        
        Args:
            query: SQL query to validate
            user_session: User session
            datasource: Target data source
            tables: Tables being accessed
            
        Returns:
            Tuple of (is_valid, list_of_violations)
        """
        violations = []
        
        # Check SQL injection
        injection_violations = self._check_sql_injection(query)
        violations.extend(injection_violations)
        
        # Check dangerous keywords
        keyword_violations = self._check_dangerous_keywords(query)
        violations.extend(keyword_violations)
        
        # Check query length
        length_violations = self._check_query_length(query)
        violations.extend(length_violations)
        
        # Check user permissions
        permission_violations = self._check_user_permissions(user_session, datasource, tables)
        violations.extend(permission_violations)
        
        # Check allowed operations
        operation_violations = self._check_allowed_operations(query)
        violations.extend(operation_violations)
        
        # Log violations
        for violation in violations:
            self.violations.append(violation)
            logger.warning(f"Security violation: {violation.violation_type} - {violation.description}")
        
        is_valid = len(violations) == 0
        return is_valid, violations
    
    def _check_sql_injection(self, query: str) -> List[SecurityViolation]:
        """Check for SQL injection patterns"""
        violations = []
        
        if not self.security_settings.get('enable_sql_injection_protection', True):
            return violations
        
        query_lower = query.lower()
        
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                violations.append(SecurityViolation(
                    violation_type="SQL_INJECTION",
                    description=f"Potential SQL injection detected: pattern '{pattern}'",
                    severity="HIGH",
                    query=query,
                    timestamp=datetime.now()
                ))
        
        return violations
    
    def _check_dangerous_keywords(self, query: str) -> List[SecurityViolation]:
        """Check for dangerous SQL keywords"""
        violations = []
        
        query_upper = query.upper()
        
        for keyword in self.dangerous_keywords:
            if keyword in query_upper:
                violations.append(SecurityViolation(
                    violation_type="DANGEROUS_KEYWORD",
                    description=f"Dangerous keyword detected: {keyword}",
                    severity="CRITICAL",
                    query=query,
                    timestamp=datetime.now()
                ))
        
        return violations
    
    def _check_query_length(self, query: str) -> List[SecurityViolation]:
        """Check query length limits"""
        violations = []
        
        max_length = self.security_settings.get('max_query_length', 10000)
        
        if len(query) > max_length:
            violations.append(SecurityViolation(
                violation_type="QUERY_TOO_LONG",
                description=f"Query exceeds maximum length: {len(query)} > {max_length}",
                severity="MEDIUM",
                query=query[:100] + "...",  # Truncate for logging
                timestamp=datetime.now()
            ))
        
        return violations
    
    def _check_user_permissions(self, user_session: UserSession, 
                               datasource: str, tables: List[str]) -> List[SecurityViolation]:
        """Check user permissions for data access"""
        violations = []
        
        for table in tables:
            if not self.config_manager.validate_user_permissions(user_session.role, datasource, table):
                violations.append(SecurityViolation(
                    violation_type="ACCESS_DENIED",
                    description=f"User {user_session.user_id} (role: {user_session.role}) does not have permission to access {datasource}.{table}",
                    severity="HIGH",
                    query="",
                    timestamp=datetime.now()
                ))
        
        return violations
    
    def _check_allowed_operations(self, query: str) -> List[SecurityViolation]:
        """Check if query uses only allowed operations"""
        violations = []
        
        allowed_operations = self.security_settings.get('allowed_operations', ['SELECT'])
        query_upper = query.upper().strip()
        
        # Check if query starts with an allowed operation
        is_allowed = False
        for operation in allowed_operations:
            if query_upper.startswith(operation.upper()):
                is_allowed = True
                break
        
        if not is_allowed:
            violations.append(SecurityViolation(
                violation_type="OPERATION_NOT_ALLOWED",
                description=f"Query operation not in allowed list: {allowed_operations}",
                severity="HIGH",
                query=query,
                timestamp=datetime.now()
            ))
        
        return violations
    
    def _get_role_permissions(self, role: str) -> List[str]:
        """Get permissions for a user role"""
        roles = self.access_control.get('roles', {})
        role_config = roles.get(role, {})
        return role_config.get('permissions', [])
    
    def sanitize_query(self, query: str) -> str:
        """
        Sanitize SQL query to prevent injection
        
        Args:
            query: Original SQL query
            
        Returns:
            Sanitized SQL query
        """
        # Remove comments
        query = re.sub(r'--.*$', '', query, flags=re.MULTILINE)
        query = re.sub(r'/\*.*?\*/', '', query, flags=re.DOTALL)
        
        # Remove extra whitespace
        query = ' '.join(query.split())
        
        # Escape single quotes (basic protection)
        query = query.replace("'", "''")
        
        return query
    
    def add_row_level_security(self, query: str, user_session: UserSession, 
                              datasource: str, table: str) -> str:
        """
        Add row-level security filters to query
        
        Args:
            query: Original SQL query
            user_session: User session
            datasource: Data source name
            table: Table name
            
        Returns:
            Modified query with security filters
        """
        if not self.security_settings.get('enable_row_level_security', True):
            return query
        
        # Example row-level security rules
        security_filters = {
            'sales_user': {
                'sales_transactions': "sales_rep_id = {user_id}",
                'customers': "created_by = '{user_id}'"
            },
            'hr_user': {
                'employees': "department IN ('HR', 'Admin')"
            }
        }
        
        role_filters = security_filters.get(user_session.role, {})
        table_filter = role_filters.get(table)
        
        if table_filter:
            # Replace placeholders
            table_filter = table_filter.format(user_id=user_session.user_id)
            
            # Add WHERE clause or extend existing one
            if 'WHERE' in query.upper():
                query = query.replace('WHERE', f'WHERE ({table_filter}) AND', 1)
            else:
                # Find the position to insert WHERE clause
                from_match = re.search(r'\bFROM\s+\w+', query, re.IGNORECASE)
                if from_match:
                    insert_pos = from_match.end()
                    query = query[:insert_pos] + f' WHERE {table_filter}' + query[insert_pos:]
        
        return query
    
    def get_security_report(self) -> Dict[str, Any]:
        """
        Generate security report
        
        Returns:
            Security report with violations and statistics
        """
        now = datetime.now()
        last_24h = now - timedelta(hours=24)
        
        # Filter recent violations
        recent_violations = [v for v in self.violations if v.timestamp >= last_24h]
        
        # Count violations by type
        violation_counts = {}
        for violation in recent_violations:
            violation_counts[violation.violation_type] = violation_counts.get(violation.violation_type, 0) + 1
        
        # Active sessions
        active_session_count = len(self.active_sessions)
        
        return {
            "timestamp": now.isoformat(),
            "active_sessions": active_session_count,
            "total_violations": len(self.violations),
            "recent_violations_24h": len(recent_violations),
            "violation_counts": violation_counts,
            "security_settings": self.security_settings,
            "recent_violations": [
                {
                    "type": v.violation_type,
                    "description": v.description,
                    "severity": v.severity,
                    "timestamp": v.timestamp.isoformat()
                }
                for v in recent_violations[-10:]  # Last 10 violations
            ]
        }
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        now = datetime.now()
        expired_tokens = [
            token for token, session in self.active_sessions.items()
            if session.expires_at <= now
        ]
        
        for token in expired_tokens:
            user_id = self.active_sessions[token].user_id
            del self.active_sessions[token]
            logger.info(f"Cleaned up expired session for user: {user_id}")
        
        if expired_tokens:
            logger.info(f"Cleaned up {len(expired_tokens)} expired sessions")
