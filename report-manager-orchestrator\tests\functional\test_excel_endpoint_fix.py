#!/usr/bin/env python3
"""
Test the fixed Excel endpoint to ensure it returns ONLY tax code data
"""

import requests
import json
import time

def test_excel_endpoint():
    """Test the /query/excel endpoint"""
    
    print("🧪 Testing Fixed Excel Endpoint")
    print("=" * 50)
    
    # Test data
    test_query = "I need all einvoicing Tax code"
    
    print(f"📝 Query: '{test_query}'")
    print(f"🎯 Expected: ONLY tax code data (no Customer, Metric, Report, Sales)")
    print("-" * 50)
    
    try:
        # Make request to Excel endpoint
        url = "http://localhost:8000/query/excel"
        payload = {"text": test_query}
        
        print(f"🌐 Calling: POST {url}")
        print(f"📤 Payload: {json.dumps(payload)}")
        
        response = requests.post(url, json=payload, timeout=10)
        
        print(f"📥 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Response received successfully!")
            print("-" * 30)
            
            # Check response structure
            print("📊 Response Analysis:")
            print(f"   Success: {data.get('success', False)}")
            
            if 'metadata' in data:
                metadata = data['metadata']
                print(f"   Title: {metadata.get('title', 'N/A')}")
                print(f"   SQL Query: {metadata.get('sql_query', 'N/A')}")
                print(f"   Confidence: {metadata.get('confidence', 'N/A')}")
            
            if 'sheets' in data:
                sheets = data['sheets']
                print(f"   Number of sheets: {len(sheets)}")
                
                for i, sheet in enumerate(sheets):
                    print(f"   Sheet {i+1}: {sheet.get('name', 'Unnamed')}")
                    print(f"     Headers: {sheet.get('headers', [])}")
                    print(f"     Rows: {len(sheet.get('rows', []))}")
            
            if 'summary' in data:
                summary = data['summary']
                print(f"   Datasets returned: {summary.get('datasets_returned', 'N/A')}")
                print(f"   Tables accessed: {summary.get('tables_accessed', [])}")
            
            # Verify it's ONLY tax code data
            print("\n🔍 Verification:")
            
            if data.get('success'):
                sheets = data.get('sheets', [])
                
                if len(sheets) == 1:
                    print("✅ CORRECT: Only 1 dataset returned")
                    
                    sheet = sheets[0]
                    sheet_name = sheet.get('name', '')
                    
                    if 'tax' in sheet_name.lower():
                        print("✅ CORRECT: Sheet contains tax code data")
                    else:
                        print(f"❌ WRONG: Sheet name is '{sheet_name}' (should contain 'tax')")
                    
                    headers = sheet.get('headers', [])
                    expected_headers = ['TaxCode', 'TaxRate', 'Description', 'TaxType', 'Active']
                    
                    if any(header in headers for header in expected_headers):
                        print("✅ CORRECT: Headers contain tax code fields")
                    else:
                        print(f"❌ WRONG: Headers are {headers} (should contain tax fields)")
                    
                    # Check for wrong data
                    wrong_fields = ['customer_id', 'name', 'revenue', 'status']
                    if any(field in headers for field in wrong_fields):
                        print(f"❌ WRONG: Contains customer data fields: {headers}")
                    else:
                        print("✅ CORRECT: No customer data fields found")
                
                else:
                    print(f"❌ WRONG: {len(sheets)} datasets returned (should be 1)")
                    for i, sheet in enumerate(sheets):
                        print(f"   Dataset {i+1}: {sheet.get('name', 'Unnamed')}")
            
            else:
                print(f"❌ Query failed: {data.get('error', 'Unknown error')}")
            
            print(f"\n📋 Full Response:")
            print(json.dumps(data, indent=2))
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server not running")
        print("💡 Start server with: python main.py")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def test_streaming_endpoint():
    """Test that streaming endpoint still works but is different"""
    
    print(f"\n🧪 Testing Streaming Endpoint (Should be Different)")
    print("=" * 50)
    
    try:
        url = "http://localhost:8000/query/stream"
        payload = {"text": "I need all einvoicing Tax code"}
        
        print(f"🌐 Calling: POST {url}")
        
        response = requests.post(url, json=payload, timeout=5, stream=True)
        
        print(f"📥 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("📡 Streaming response detected")
            
            # Read first few chunks
            chunks = []
            for i, chunk in enumerate(response.iter_content(chunk_size=1024, decode_unicode=True)):
                if chunk:
                    chunks.append(chunk)
                    if i >= 2:  # Read first 3 chunks
                        break
            
            print(f"📊 Received {len(chunks)} chunks")
            for i, chunk in enumerate(chunks):
                print(f"   Chunk {i+1}: {chunk[:100]}...")
        
    except Exception as e:
        print(f"⚠️  Streaming test failed: {e}")


def main():
    """Main test function"""
    
    print("🔧 EXCEL ENDPOINT FIX TEST")
    print("=" * 80)
    
    # Test Excel endpoint
    test_excel_endpoint()
    
    # Test streaming endpoint for comparison
    test_streaming_endpoint()
    
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY")
    print(f"{'='*80}")
    print("🎯 Expected Result:")
    print("   • /query/excel returns ONLY tax code data")
    print("   • Single dataset with tax code fields")
    print("   • No Customer, Metric, Report, or Sales data")
    print("   • Direct JSON response (not streaming)")
    
    print(f"\n💡 Frontend Integration:")
    print("   • Always call /query/excel (not /query/stream)")
    print("   • Expect single dataset response")
    print("   • Generate Excel from response.sheets[0]")


if __name__ == "__main__":
    main()
