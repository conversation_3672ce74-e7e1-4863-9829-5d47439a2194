"""
User Context Module

This module contains context-related classes and enums.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class TaskType(Enum):
    """Types of tasks the orchestrator can handle"""
    QUERY = "query"
    REPORT_GENERATION = "report_generation"
    DATA_ANALYSIS = "data_analysis"
    WORKFLOW = "workflow"
    UNKNOWN = "unknown"


@dataclass
class UserContext:
    """Context created from user input"""
    intent: str
    entities: List[str]
    task_type: TaskType
    confidence: float
    parameters: Dict[str, Any]
    original_text: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.original_text is None:
            self.original_text = self.intent
        if self.metadata is None:
            self.metadata = {}
