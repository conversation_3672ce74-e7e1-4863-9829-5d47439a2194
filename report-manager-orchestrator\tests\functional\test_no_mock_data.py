#!/usr/bin/env python3
"""
Test to verify all mock data has been removed from the project
"""

import requests
import json

def test_excel_endpoint_no_mock():
    """Test that Excel endpoint doesn't return mock data"""
    try:
        url = "http://localhost:8000/query/excel"
        
        # Test tax code query
        data = {"text": "I need all einvoicing Tax code"}
        response = requests.post(url, json=data, timeout=5)
        
        print("🧪 Testing Tax Code Query (No Mock Data)")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') == False:
                print("✅ CORRECT: Returns error when database not configured")
                print(f"Message: {result.get('error', 'No error message')}")
                
                if 'setup_instructions' in result:
                    print("✅ CORRECT: Provides setup instructions")
                else:
                    print("⚠️  Missing setup instructions")
            else:
                print("❌ WRONG: Should return error when database not configured")
                print(f"Response: {json.dumps(result, indent=2)}")
        
        # Test non-tax query
        data = {"text": "Show me customer data"}
        response = requests.post(url, json=data, timeout=5)
        
        print(f"\n🧪 Testing Customer Data Query (Should Fail)")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success') == False:
                print("✅ CORRECT: Returns error for non-tax queries")
                print(f"Error: {result.get('error', 'No error message')}")
            else:
                print("❌ WRONG: Should not return customer data")
                
                # Check if it contains customer data
                sheets = result.get('sheets', [])
                for sheet in sheets:
                    headers = sheet.get('headers', [])
                    if any(header in ['customer_id', 'name', 'revenue', 'status'] for header in headers):
                        print("❌ CRITICAL: Still contains customer mock data!")
                        return False
                
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠️  Server not running - start with: python main.py")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_regular_endpoint():
    """Test regular endpoint doesn't return mock data"""
    try:
        url = "http://localhost:8000/query"
        data = {"text": "Show me all data"}
        
        response = requests.post(url, json=data, timeout=5)
        
        print(f"\n🧪 Testing Regular Query Endpoint")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if result contains mock data
            result_data = result.get('result', {})
            
            # Look for mock data indicators
            mock_indicators = [
                'Customer 1', 'Customer 2', 'Product A', 'Product B',
                'Acme Corp', 'Tech Solutions', 'Q1 Sales Report'
            ]
            
            result_str = json.dumps(result_data).lower()
            
            found_mock = []
            for indicator in mock_indicators:
                if indicator.lower() in result_str:
                    found_mock.append(indicator)
            
            if found_mock:
                print(f"❌ CRITICAL: Found mock data: {found_mock}")
                return False
            else:
                print("✅ CORRECT: No mock data found in response")
                return True
        
        return True
        
    except Exception as e:
        print(f"⚠️  Regular endpoint test failed: {e}")
        return True  # Not critical


def main():
    """Main test function"""
    print("🔍 TESTING: NO MOCK DATA IN PROJECT")
    print("=" * 60)
    
    # Test Excel endpoint
    excel_ok = test_excel_endpoint_no_mock()
    
    # Test regular endpoint
    regular_ok = test_regular_endpoint()
    
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    if excel_ok and regular_ok:
        print("✅ SUCCESS: All mock data removed!")
        print("🎯 Project is now clean and production-ready")
        print("📝 Next steps:")
        print("   1. Configure database credentials in .env")
        print("   2. Test with real database")
        print("   3. Deploy to production")
    else:
        print("❌ FAILED: Mock data still present")
        print("🔧 Action required: Remove remaining mock data")
    
    print(f"\n💡 Expected behavior:")
    print("   • Tax code queries: Require database configuration")
    print("   • Other queries: Return configuration error")
    print("   • No hardcoded customer/sales/metric data")
    print("   • Setup instructions provided")


if __name__ == "__main__":
    main()
