/* Thinking Message Styles (ChatGPT-style) */
.thinking-container {
  background: #f8f9fa;
  border-radius: 18px;
  padding: 16px 20px;
  margin: 10px 0;
  animation: fadeIn 300ms ease-out;
  max-width: fit-content;
}

.thinking-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.thinking-text {
  color: #666;
  font-size: 14px;
  font-weight: 400;
}

.skip-button {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 200ms ease-in-out;
  text-decoration: underline;
}

.skip-button:hover {
  background: #e9ecef;
  color: #495057;
}

.skip-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.25);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 600px) {
  .thinking-container {
    padding: 12px 16px;
  }

  .thinking-text {
    font-size: 13px;
  }

  .skip-button {
    font-size: 13px;
    padding: 3px 6px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .thinking-container {
    border: 2px solid #000;
  }

  .skip-button {
    border: 1px solid #666;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .thinking-container,
  .skip-button {
    transition: none;
    animation: none;
  }
}
