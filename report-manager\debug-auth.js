// Debug script to check auth configuration
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Auth Configuration Debug\n');

// Check environment variables
console.log('Environment Variables:');
console.log('VITE_DEMO_MODE:', process.env.VITE_DEMO_MODE);
console.log('VITE_GOOGLE_CLIENT_ID:', process.env.VITE_GOOGLE_CLIENT_ID);
console.log('VITE_REDIRECT_URI:', process.env.VITE_REDIRECT_URI);

// Simulate the auth config logic
const clientId = process.env.VITE_GOOGLE_CLIENT_ID || 'demo-mode';

console.log('\nAuth Config Values:');
console.log('clientId:', clientId);

// Check each demo mode condition
const conditions = {
  'VITE_DEMO_MODE === "true"': process.env.VITE_DEMO_MODE === 'true',
  'clientId === "demo-mode"': clientId === 'demo-mode',
  'clientId === "your-actual-client-id.apps.googleusercontent.com"': clientId === 'your-actual-client-id.apps.googleusercontent.com',
  '!clientId': !clientId,
  'clientId.includes("your-actual-client-id")': clientId.includes('your-actual-client-id')
};

console.log('\nDemo Mode Conditions:');
Object.entries(conditions).forEach(([condition, result]) => {
  console.log(`${result ? '✅' : '❌'} ${condition}: ${result}`);
});

const demoMode = Object.values(conditions).some(condition => condition);
console.log('\n🎯 Final DEMO_MODE result:', demoMode);

if (demoMode) {
  console.log('\n⚠️  OAuth is disabled because demo mode is active');
  console.log('To fix this, ensure:');
  console.log('1. VITE_DEMO_MODE is set to "false" (or not set)');
  console.log('2. VITE_GOOGLE_CLIENT_ID is set to your real Google Client ID');
  console.log('3. The Client ID does not contain "your-actual-client-id"');
} else {
  console.log('\n✅ OAuth should be working - demo mode is disabled');
}
