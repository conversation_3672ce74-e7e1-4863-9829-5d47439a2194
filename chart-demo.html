<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart + Excel Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .demo-button:hover {
            background: #0056b3;
        }
        .chat-message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .chart-container {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .download-card {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 15px 0;
        }
        .file-icon {
            width: 40px;
            height: 40px;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            font-size: 18px;
        }
        .file-details {
            flex: 1;
        }
        .file-name {
            font-weight: 600;
            color: #333;
        }
        .file-meta {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }
        .chart-context {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 4px;
            padding: 2px 6px;
            background: #e3f2fd;
            border-radius: 4px;
            border: 1px solid #bbdefb;
        }
        .chart-indicator {
            font-size: 12px;
        }
        .chart-text {
            font-size: 11px;
            color: #1976d2;
            font-weight: 500;
        }
        .download-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
        }
        .download-btn:hover {
            background: #2563eb;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background: #e9ecef;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Chart + Excel Integration Demo</h1>
        
        <div class="demo-section">
            <h3>🎯 Complete Workflow Demo</h3>
            <p>This demonstrates how a single query generates both charts and downloadable Excel files.</p>
            
            <button class="demo-button" onclick="simulateChartQuery()">
                🍩 Simulate: "Give me donut chart for employee salary"
            </button>
            
            <div id="demo-result"></div>
        </div>

        <div class="demo-section">
            <h3>📋 How It Works</h3>
            <div class="result">
<strong>1. User Query:</strong> "Give me donut chart for all employee salary"

<strong>2. Backend Processing:</strong>
   • Detects chart keywords: "donut chart"
   • Queries employee database
   • Generates chart data AND Excel data
   • Streams both in single response

<strong>3. Frontend Display:</strong>
   • Shows chart visualization in chat
   • Provides Excel download button
   • Both from same API call

<strong>4. User Experience:</strong>
   ✅ Sees chart immediately
   ✅ Can download Excel file
   ✅ Chart context shown in download
   ✅ Single query, dual output
            </div>
        </div>
    </div>

    <script>
        function simulateChartQuery() {
            const resultDiv = document.getElementById('demo-result');
            
            // Simulate the chat message with chart
            resultDiv.innerHTML = `
                <div class="chat-message">
                    <strong>Assistant:</strong> Here's your employee salary donut chart and Excel report:
                </div>
                
                <div class="chart-container">
                    <canvas id="demo-chart" width="400" height="400"></canvas>
                </div>
                
                <div class="download-card">
                    <div class="file-icon">📊</div>
                    <div class="file-details">
                        <div class="file-name">Employee_Salary_Report_${Date.now()}.xlsx</div>
                        <div class="file-meta">
                            <span>2.3 KB</span> • <span>Excel Spreadsheet</span>
                        </div>
                        <div class="chart-context">
                            <span class="chart-indicator">📊</span>
                            <span class="chart-text">Generated with 1 chart(s)</span>
                        </div>
                    </div>
                    <button class="download-btn" onclick="simulateDownload()">⬇️</button>
                </div>
            `;

            // Create the actual chart
            const ctx = document.getElementById('demo-chart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations'],
                    datasets: [{
                        data: [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', 
                            '#4BC0C0', '#9966FF', '#FF9F40'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Employee Salary Distribution by Department',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: {
                            position: 'bottom',
                            labels: { padding: 20, usePointStyle: true }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: $${value.toLocaleString()} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });

            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function simulateDownload() {
            alert('📊 Excel file download started!\n\nIn the real app, this would download an Excel file containing:\n• Employee salary data\n• Chart data\n• Summary statistics');
        }
    </script>
</body>
</html>
