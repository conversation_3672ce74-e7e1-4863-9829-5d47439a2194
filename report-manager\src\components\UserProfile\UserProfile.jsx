import React, { useState } from 'react';
import authService from '../../services/authService';
import './UserProfile.css';

const UserProfile = ({ user, onSignOut }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await authService.signOut();
      if (onSignOut) {
        onSignOut();
      }
    } catch (error) {
      console.error('Sign-out failed:', error);
    } finally {
      setIsSigningOut(false);
      setIsDropdownOpen(false);
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-profile')) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  if (!user) return null;

  return (
    <div className="user-profile">
      <button 
        className="profile-trigger"
        onClick={toggleDropdown}
        title={`Signed in as ${user.name || user.email}`}
      >
        <div className="profile-avatar">
          {user.picture ? (
            <img 
              src={user.picture} 
              alt={user.name || user.email}
              className="avatar-image"
            />
          ) : (
            <div className="avatar-placeholder">
              {(user.name || user.email || 'U').charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className="profile-info">
          <div className="profile-name">
            {user.name || user.email?.split('@')[0] || 'User'}
          </div>
          <div className="profile-email">
            {user.email}
          </div>
        </div>
        <div className={`dropdown-arrow ${isDropdownOpen ? 'open' : ''}`}>
          ▼
        </div>
      </button>

      {isDropdownOpen && (
        <div className="profile-dropdown">
          <div className="dropdown-header">
            <div className="dropdown-avatar">
              {user.picture ? (
                <img 
                  src={user.picture} 
                  alt={user.name || user.email}
                  className="dropdown-avatar-image"
                />
              ) : (
                <div className="dropdown-avatar-placeholder">
                  {(user.name || user.email || 'U').charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <div className="dropdown-info">
              <div className="dropdown-name">
                {user.name || user.email?.split('@')[0] || 'User'}
              </div>
              <div className="dropdown-email">
                {user.email}
              </div>
            </div>
          </div>

          <div className="dropdown-divider"></div>

          <div className="dropdown-menu">
            <button 
              className="dropdown-item"
              onClick={() => {
                setIsDropdownOpen(false);
                // Add profile management functionality here
              }}
            >
              <span className="dropdown-icon">👤</span>
              <span>Manage Account</span>
            </button>
            
            <button 
              className="dropdown-item"
              onClick={() => {
                setIsDropdownOpen(false);
                // Add settings functionality here
              }}
            >
              <span className="dropdown-icon">⚙️</span>
              <span>Settings</span>
            </button>
          </div>

          <div className="dropdown-divider"></div>

          <button 
            className={`dropdown-item sign-out ${isSigningOut ? 'loading' : ''}`}
            onClick={handleSignOut}
            disabled={isSigningOut}
          >
            {isSigningOut ? (
              <>
                <div className="signout-spinner"></div>
                <span>Signing out...</span>
              </>
            ) : (
              <>
                <span className="dropdown-icon">🚪</span>
                <span>Sign Out</span>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default UserProfile;
