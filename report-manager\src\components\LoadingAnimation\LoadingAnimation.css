.loading-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  margin: 20px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.loading-container {
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 25px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #3b82f6;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #10b981;
  animation-delay: 0.3s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #f59e0b;
  animation-delay: 0.6s;
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-messages {
  margin-bottom: 20px;
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 0;
  font-size: 14px;
  transition: all 0.3s ease;
  opacity: 0.4;
}

.loading-message.active {
  opacity: 1;
  color: #3b82f6;
  font-weight: 500;
  transform: scale(1.02);
}

.loading-message.completed {
  opacity: 0.7;
  color: #10b981;
}

.loading-message.pending {
  opacity: 0.3;
  color: #6b7280;
}

.check-mark {
  color: #10b981;
  font-weight: bold;
  margin-right: 8px;
  font-size: 12px;
}

.loading-dots {
  color: #3b82f6;
  margin-right: 8px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.message-text {
  flex: 1;
  text-align: left;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 2px;
  transition: width 0.5s ease;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

.loading-subtitle {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
  margin-top: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
  .loading-animation {
    padding: 20px 15px;
    margin: 15px 0;
  }
  
  .loading-container {
    max-width: 300px;
  }
  
  .loading-spinner {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }
  
  .loading-message {
    font-size: 13px;
    padding: 6px 0;
  }
  
  .loading-subtitle {
    font-size: 11px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .loading-animation {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #4b5563;
  }
  
  .loading-message.completed {
    color: #34d399;
  }
  
  .loading-message.pending {
    color: #9ca3af;
  }
  
  .loading-subtitle {
    color: #9ca3af;
  }
  
  .progress-bar {
    background: #4b5563;
  }
}
