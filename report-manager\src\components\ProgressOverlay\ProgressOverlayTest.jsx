import React, { useState } from 'react';
import ProgressOverlay from './ProgressOverlay';

/**
 * Test component for ProgressOverlay
 * This component allows testing the overlay functionality
 */
const ProgressOverlayTest = () => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (result) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      timestamp: new Date().toLocaleTimeString(),
      ...result
    }]);
  };

  const startTest = () => {
    setShowOverlay(true);
    addTestResult({
      type: 'info',
      message: 'Progress overlay started'
    });
  };

  const handleComplete = () => {
    setShowOverlay(false);
    addTestResult({
      type: 'success',
      message: 'Progress overlay completed successfully'
    });
  };

  const handleCancel = () => {
    setShowOverlay(false);
    addTestResult({
      type: 'warning',
      message: 'Progress overlay cancelled by user'
    });
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Progress Overlay Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={startTest}
          disabled={showOverlay}
          style={{
            padding: '10px 20px',
            backgroundColor: '#0078d4',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: showOverlay ? 'not-allowed' : 'pointer',
            opacity: showOverlay ? 0.6 : 1,
            marginRight: '10px'
          }}
        >
          {showOverlay ? 'Test Running...' : 'Start Progress Test'}
        </button>
        
        <button 
          onClick={clearResults}
          style={{
            padding: '10px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear Results
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Test Configuration:</h3>
        <ul>
          <li>Total Duration: 13 seconds</li>
          <li>Steps: 3 (Query agent → Report agent → Generate report)</li>
          <li>Step Duration: ~4.3 seconds each</li>
          <li>Artificial Delay: Enabled</li>
          <li>Cancel Button: Available</li>
        </ul>
      </div>

      <div>
        <h3>Test Results:</h3>
        {testResults.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>
            No test results yet. Click "Start Progress Test" to begin.
          </p>
        ) : (
          <div style={{ 
            maxHeight: '300px', 
            overflowY: 'auto',
            border: '1px solid #ddd',
            borderRadius: '4px',
            padding: '10px'
          }}>
            {testResults.map(result => (
              <div 
                key={result.id}
                style={{
                  padding: '8px',
                  marginBottom: '5px',
                  borderRadius: '4px',
                  backgroundColor: 
                    result.type === 'success' ? '#d4edda' :
                    result.type === 'warning' ? '#fff3cd' :
                    result.type === 'error' ? '#f8d7da' :
                    '#d1ecf1',
                  borderLeft: `4px solid ${
                    result.type === 'success' ? '#28a745' :
                    result.type === 'warning' ? '#ffc107' :
                    result.type === 'error' ? '#dc3545' :
                    '#17a2b8'
                  }`
                }}
              >
                <strong>{result.timestamp}</strong> - {result.message}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Progress Overlay */}
      <ProgressOverlay
        isVisible={showOverlay}
        onCancel={handleCancel}
        onComplete={handleComplete}
        enableArtificialDelay={true}
        totalDelayMs={13000}
        steps={[
          "Requesting Query agent for parsing",
          "Requesting Report agent to create report",
          "Generating report for your request"
        ]}
        strings={{
          cancel: "Cancel",
          processing: "Processing..."
        }}
      />
    </div>
  );
};

export default ProgressOverlayTest;
