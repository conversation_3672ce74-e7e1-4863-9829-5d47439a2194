#!/usr/bin/env python3
"""
Test script to verify OpenAI setup

This script tests if OpenAI is properly configured and working.
"""

import os
import sys
from dotenv import load_dotenv

def test_openai_setup():
    """Test OpenAI configuration"""
    print("🔍 Testing OpenAI Setup...")
    
    # Load environment variables
    load_dotenv()
    
    # Check if OpenAI API key is set
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key or api_key == "your_openai_api_key_here":
        print("❌ OpenAI API key not set!")
        print("   Please edit .env file and set OPENAI_API_KEY=your_actual_openai_key")
        print("   You can get an API key from: https://platform.openai.com/api-keys")
        return False
    
    print(f"✅ OpenAI API key found: {api_key[:10]}...")
    
    # Test OpenAI import
    try:
        from langchain_openai import ChatOpenAI
        print("✅ LangChain OpenAI import successful")
    except ImportError as e:
        print(f"❌ Failed to import LangChain OpenAI: {e}")
        return False
    
    # Test OpenAI initialization
    try:
        llm = ChatOpenAI(
            api_key=api_key,
            model=os.getenv("OPENAI_MODEL", "gpt-4"),
            temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.1"))
        )
        print("✅ OpenAI LLM initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize OpenAI LLM: {e}")
        return False
    
    # Test a simple API call (optional - requires valid API key)
    try:
        from langchain.schema import HumanMessage
        
        print("🧪 Testing OpenAI API call...")
        response = llm.invoke([HumanMessage(content="Say 'Hello, OpenAI is working!'")])
        print(f"✅ OpenAI API call successful: {response.content}")
        return True
        
    except Exception as e:
        print(f"⚠️  OpenAI API call failed: {e}")
        print("   This might be due to:")
        print("   - Invalid API key")
        print("   - Network issues")
        print("   - API quota exceeded")
        print("   - Model not available")
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("🎯 OpenAI Setup Test")
    print("=" * 60)
    
    success = test_openai_setup()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 OpenAI setup is working correctly!")
        print("\nYou can now start the API server:")
        print("   python main.py serve")
        print("   or")
        print("   python start_api.py")
    else:
        print("❌ OpenAI setup needs attention.")
        print("\nPlease:")
        print("1. Get an OpenAI API key from: https://platform.openai.com/api-keys")
        print("2. Edit .env file and set OPENAI_API_KEY=your_actual_key")
        print("3. Run this test again: python test_openai_setup.py")

if __name__ == "__main__":
    main()
