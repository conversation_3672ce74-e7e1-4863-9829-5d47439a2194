<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Test</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <h1>Google OAuth Test</h1>
    <div id="status">Loading...</div>
    <div id="g_id_onload"
         data-client_id="***********-lr3mkvm59r5nvofonf6otnm5ju6oa6tq.apps.googleusercontent.com"
         data-callback="handleCredentialResponse">
    </div>
    <div class="g_id_signin" data-type="standard"></div>

    <script>
        function handleCredentialResponse(response) {
            console.log("Encoded JWT ID token: " + response.credential);
            document.getElementById('status').innerHTML = '✅ OAuth working! Check console for token.';
        }

        window.onload = function() {
            if (window.google) {
                document.getElementById('status').innerHTML = '✅ Google Identity Services loaded';
            } else {
                document.getElementById('status').innerHTML = '❌ Google Identity Services failed to load';
            }
        }

        // Check if script loads
        setTimeout(() => {
            if (window.google && window.google.accounts) {
                document.getElementById('status').innerHTML = '✅ Google Identity Services ready';
            } else {
                document.getElementById('status').innerHTML = '❌ Google Identity Services not available after 3 seconds';
            }
        }, 3000);
    </script>
</body>
</html>
