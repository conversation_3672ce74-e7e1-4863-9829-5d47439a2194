#!/usr/bin/env python3
"""
Simple test server for chart functionality
"""
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, AsyncGenerator
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

app = FastAPI(title="Chart Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class StreamQueryRequest(BaseModel):
    text: str
    output_format: str = "excel"
    chunk_size: int = 50

def generate_chart_from_query(query: str) -> Optional[Dict]:
    """Generate chart data based on query"""
    query_lower = query.lower()
    
    # Check if it's a chart request
    chart_keywords = ['chart', 'graph', 'visualization', 'donut', 'pie', 'bar', 'plot']
    if not any(keyword in query_lower for keyword in chart_keywords):
        return None
    
    # Determine chart type
    chart_type = "donut"  # default
    if "bar" in query_lower:
        chart_type = "bar"
    elif "pie" in query_lower:
        chart_type = "pie"
    elif "donut" in query_lower or "doughnut" in query_lower:
        chart_type = "donut"
    
    # Generate sample salary data
    return {
        "type": chart_type,
        "title": "Employee Salary Distribution by Department",
        "data": {
            "labels": ["Engineering", "Sales", "Marketing", "HR", "Finance", "Operations"],
            "values": [2500000, 1800000, 1200000, 950000, 1600000, 1100000],
            "datasetLabel": "Salary",
            "summary": [
                {"label": "Departments", "value": "6"},
                {"label": "Total Employees", "value": "150"},
                {"label": "Avg Salary", "value": "$85,000"}
            ]
        },
        "options": {
            "width": 500,
            "height": 400
        }
    }

def generate_excel_data(query: str) -> Dict:
    """Generate Excel data"""
    return {
        "metadata": {
            "title": f"Report: {query}",
            "generated_at": datetime.now().isoformat(),
            "query": query
        },
        "sheets": [{
            "name": "Employee_Salaries",
            "headers": ["Department", "Employee_Count", "Total_Salary", "Avg_Salary"],
            "rows": [
                ["Engineering", 45, 2500000, 55556],
                ["Sales", 30, 1800000, 60000],
                ["Marketing", 20, 1200000, 60000],
                ["HR", 15, 950000, 63333],
                ["Finance", 25, 1600000, 64000],
                ["Operations", 15, 1100000, 73333]
            ],
            "total_rows": 6
        }]
    }

@app.get("/")
async def root():
    return {"message": "Chart Test Server", "status": "running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "chart_functionality": "enabled"
    }

@app.post("/query/stream")
async def stream_query(request: StreamQueryRequest):
    """Streaming endpoint with chart support"""
    
    async def generate_stream() -> AsyncGenerator[str, None]:
        try:
            # Send initial metadata
            metadata = {
                "type": "metadata",
                "query": request.text,
                "timestamp": datetime.now().isoformat(),
                "format": request.output_format
            }
            yield f"data: {json.dumps(metadata)}\n\n"
            
            # Check for chart request
            chart_data = generate_chart_from_query(request.text)
            chart_generated = chart_data is not None
            
            if chart_generated:
                print(f"📊 CHART DETECTED: {request.text}")
                # Send chart data
                chart_payload = {
                    "type": "chart",
                    "chart": chart_data,
                    "timestamp": datetime.now().isoformat(),
                    "ai_generated": True
                }
                yield f"data: {json.dumps(chart_payload)}\n\n"
            
            # Generate and send Excel data
            excel_data = generate_excel_data(request.text)
            
            # Send Excel data in chunks
            for i, sheet in enumerate(excel_data["sheets"]):
                # Send headers
                headers_chunk = {
                    "type": "data",
                    "chunk": {
                        "type": "headers",
                        "sheet_name": sheet["name"],
                        "data": sheet["headers"]
                    },
                    "timestamp": datetime.now().isoformat()
                }
                yield f"data: {json.dumps(headers_chunk)}\n\n"
                
                # Send rows in chunks
                rows = sheet["rows"]
                chunk_size = request.chunk_size
                for j in range(0, len(rows), chunk_size):
                    chunk_rows = rows[j:j + chunk_size]
                    rows_chunk = {
                        "type": "data",
                        "chunk": {
                            "type": "rows",
                            "sheet_name": sheet["name"],
                            "data": chunk_rows
                        },
                        "timestamp": datetime.now().isoformat()
                    }
                    yield f"data: {json.dumps(rows_chunk)}\n\n"
            
            # Send completion signal
            completion = {
                "type": "complete",
                "success": True,
                "chart_generated": chart_generated,
                "chart_type": chart_data["type"] if chart_data else None,
                "agents_used": ["test_chart_engine"],
                "execution_time": 0.5,
                "error": None
            }
            yield f"data: {json.dumps(completion)}\n\n"
            
        except Exception as e:
            error_payload = {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(error_payload)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Chart Test Server...")
    print("📊 Chart functionality enabled")
    print("🌐 Server will be available at: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
