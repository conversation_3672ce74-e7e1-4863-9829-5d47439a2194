{"system": {"name": "Report Manager Orchestrator", "version": "1.0.0", "description": "AI-powered orchestrator that processes user text, creates context with LLM, and coordinates agent calls"}, "agents": {"router_agent": {"name": "Router Agent", "description": "Routes requests to appropriate specialized agents based on context", "capabilities": ["routing", "agent_selection", "workflow_planning"], "priority": 1}, "query_agent": {"name": "Query Agent", "description": "Handles query processing, data retrieval, and basic analysis", "capabilities": ["data_retrieval", "filtering", "aggregation", "search", "basic_analysis"], "priority": 1}, "report_agent": {"name": "Report Agent", "description": "Generates reports and handles formatting", "capabilities": ["report_creation", "formatting", "visualization"], "priority": 2}, "workflow_agent": {"name": "Workflow Agent", "description": "Manages complex workflows and process automation", "capabilities": ["process_automation", "task_scheduling", "coordination"], "priority": 3}, "analysis_agent": {"name": "Analysis Agent", "description": "Performs advanced data analysis and insights generation", "capabilities": ["statistical_analysis", "trend_analysis", "insights"], "priority": 2}}, "task_types": {"query": {"name": "Query", "description": "Simple data queries and retrieval", "default_agents": ["query_agent"]}, "report_generation": {"name": "Report Generation", "description": "Creating formatted reports", "default_agents": ["query_agent", "report_agent"]}, "data_analysis": {"name": "Data Analysis", "description": "Complex data analysis and insights", "default_agents": ["query_agent", "analysis_agent"]}, "workflow": {"name": "Workflow", "description": "Process automation and workflow management", "default_agents": ["workflow_agent"]}}, "schemas": {"user_context": {"type": "object", "properties": {"original_text": {"type": "string"}, "intent": {"type": "string"}, "task_type": {"type": "string", "enum": ["query", "report_generation", "data_analysis", "workflow", "unknown"]}, "entities": {"type": "array", "items": {"type": "string"}}, "parameters": {"type": "object"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "metadata": {"type": "object"}}, "required": ["original_text", "intent", "task_type", "entities", "parameters", "confidence"]}, "orchestration_result": {"type": "object", "properties": {"success": {"type": "boolean"}, "result": {}, "context": {"$ref": "#/schemas/user_context"}, "agents_used": {"type": "array", "items": {"type": "string"}}, "execution_time": {"type": "number"}, "error": {"type": "string"}}, "required": ["success", "result", "context", "agents_used", "execution_time"]}}}