import React, { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title
} from 'chart.js';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend, Title);

const ChartTest = () => {
  const canvasRef = useRef(null);
  const chartRef = useRef(null);

  useEffect(() => {
    console.log('🧪 ChartTest: Component mounted');
    console.log('🧪 ChartJS version:', ChartJS.version);
    
    if (canvasRef.current) {
      console.log('🧪 Canvas element found, creating chart...');
      
      // Destroy existing chart if it exists
      if (chartRef.current) {
        chartRef.current.destroy();
      }

      try {
        // Create chart using Chart.js directly
        chartRef.current = new ChartJS(canvasRef.current, {
          type: 'doughnut',
          data: {
            labels: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance'],
            datasets: [{
              data: [2500000, 1800000, 1200000, 950000, 1600000],
              backgroundColor: [
                '#FF6384',
                '#36A2EB', 
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
              ],
              borderWidth: 2,
              borderColor: '#fff'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              title: {
                display: true,
                text: 'Test Chart - Employee Salary Distribution',
                font: {
                  size: 16,
                  weight: 'bold'
                }
              },
              legend: {
                position: 'bottom',
                labels: {
                  padding: 20,
                  usePointStyle: true
                }
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const label = context.label || '';
                    const value = context.parsed;
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((value / total) * 100).toFixed(1);
                    return `${label}: $${value.toLocaleString()} (${percentage}%)`;
                  }
                }
              }
            },
            cutout: '60%'
          }
        });
        
        console.log('✅ Chart created successfully!', chartRef.current);
      } catch (error) {
        console.error('❌ Error creating chart:', error);
      }
    } else {
      console.error('❌ Canvas element not found');
    }

    // Cleanup function
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      background: '#fff', 
      borderRadius: '8px', 
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      margin: '20px 0'
    }}>
      <h3>🧪 Chart.js Test Component</h3>
      <div style={{ width: '400px', height: '400px', position: 'relative' }}>
        <canvas 
          ref={canvasRef}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
      <p style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        If you see a donut chart above, Chart.js is working correctly.
      </p>
    </div>
  );
};

export default ChartTest;
