"""
Report Manager Orchestrator

AI-powered orchestrator that processes user text, creates context with LLM,
and coordinates agent calls to fulfill user requests.
"""

__version__ = "1.0.0"
__author__ = "Report Manager Team"

from .core.orchestrator import ReportManagerOrchestrator
from .core.models import UserContext, TaskType, OrchestrationResult
from .core.flow import FlowController

__all__ = [
    "ReportManagerOrchestrator",
    "UserContext", 
    "TaskType",
    "OrchestrationResult",
    "FlowController"
]