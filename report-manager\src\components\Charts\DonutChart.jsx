import React, { useEffect, useRef } from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title
} from 'chart.js';
import './DonutChart.css';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend, Title);

const DonutChart = ({
  data,
  title = "Chart",
  width = 400,
  height = 400,
  showLegend = true,
  showTooltips = true,
  colors = [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
  ]
}) => {
  const canvasRef = useRef(null);
  const chartRef = useRef(null);

  console.log('🍩 DonutChart received data:', data);
  console.log('🍩 DonutChart title:', title);

  if (!data || !data.labels || !data.values) {
    console.log('❌ DonutChart: Invalid data structure');
    return (
      <div className="chart-error">
        <p>📊 No data available for chart</p>
      </div>
    );
  }

  console.log('✅ DonutChart: Valid data, rendering chart...');

  useEffect(() => {
    if (canvasRef.current && data && data.labels && data.values) {
      console.log('🍩 Creating donut chart...');

      // Destroy existing chart if it exists
      if (chartRef.current) {
        chartRef.current.destroy();
      }

      try {
        chartRef.current = new ChartJS(canvasRef.current, {
          type: 'doughnut',
          data: {
            labels: data.labels,
            datasets: [{
              data: data.values,
              backgroundColor: colors.slice(0, data.labels.length),
              borderColor: colors.slice(0, data.labels.length).map(color => color + '80'),
              borderWidth: 2,
              hoverBorderWidth: 3,
              hoverBorderColor: '#fff'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              title: {
                display: !!title,
                text: title,
                font: {
                  size: 16,
                  weight: 'bold'
                },
                padding: {
                  top: 10,
                  bottom: 20
                }
              },
              legend: {
                display: showLegend,
                position: 'bottom',
                labels: {
                  padding: 20,
                  usePointStyle: true,
                  font: {
                    size: 12
                  }
                }
              },
              tooltip: {
                enabled: showTooltips,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#fff',
                borderWidth: 1,
                callbacks: {
                  label: function(context) {
                    const label = context.label || '';
                    const value = context.parsed;
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((value / total) * 100).toFixed(1);
                    return `${label}: ${value.toLocaleString()} (${percentage}%)`;
                  }
                }
              }
            },
            cutout: '60%',
            animation: {
              animateRotate: true,
              animateScale: true,
              duration: 1000
            }
          }
        });

        console.log('✅ Donut chart created successfully!');
      } catch (error) {
        console.error('❌ Error creating donut chart:', error);
      }
    }

    // Cleanup function
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [data, title, width, height, showLegend, showTooltips, colors]);

  return (
    <div className="donut-chart-container">
      <div className="chart-wrapper" style={{ width, height, position: 'relative' }}>
        <canvas
          ref={canvasRef}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
      {data.summary && (
        <div className="chart-summary">
          <div className="summary-item">
            <span className="summary-label">Total:</span>
            <span className="summary-value">
              {data.values.reduce((a, b) => a + b, 0).toLocaleString()}
            </span>
          </div>
          {data.summary.map((item, index) => (
            <div key={index} className="summary-item">
              <span className="summary-label">{item.label}:</span>
              <span className="summary-value">{item.value}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DonutChart;
