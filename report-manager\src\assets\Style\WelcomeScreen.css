/* DEDICATED WELCOME SCREEN STYLES - MA<PERSON>IMUM SPECIFICITY */

/* Clean professional welcome view */
.welcome-view {
  background: #ffffff !important;
}

/* Ultra-specific selector for welcome screen title */
div.chat-interface div.welcome-view div.welcome-container h1.brand-title {
  font-size: 48px !important;
  font-weight: 600 !important;
  color: #2d3748 !important;
  margin: 0 0 40px 0 !important;
  line-height: 1.2 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'Roboto', sans-serif !important;
  letter-spacing: -0.025em !important;
  text-align: center !important;
  padding: 8px 0 !important;
  display: block !important;
  /* Beautiful gradient text */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 100% !important;
  background-repeat: no-repeat !important;
}

/* Fallback with even higher specificity */
.chat-interface .welcome-view .welcome-container .brand-title {
  font-size: 48px !important;
  font-weight: 600 !important;
  color: #2d3748 !important;
  margin: 0 0 40px 0 !important;
  line-height: 1.2 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'Roboto', sans-serif !important;
  letter-spacing: -0.025em !important;
  text-align: center !important;
  padding: 8px 0 !important;
  display: block !important;
  /* Beautiful gradient text */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 100% !important;
  background-repeat: no-repeat !important;
}

/* Fallback for browsers that don't support background-clip */
@supports not (-webkit-background-clip: text) {
  .welcome-view .brand-title {
    background: none;
    color: #2d3748;
  }
}

/* Beautiful Input Container - Welcome Screen Only */
.welcome-view .welcome-input-container {
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Ultra-specific selector for input wrapper */
div.chat-interface div.welcome-view div.welcome-container div.welcome-input-container div.welcome-input-wrapper-large {
  display: flex !important;
  flex-direction: column !important;
  background: #ffffff !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 24px !important;
  padding: 24px 32px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 32px !important;
  min-height: 120px !important;
  max-width: 100% !important;
  position: relative !important;
}

/* Fallback with class chain */
.chat-interface .welcome-view .welcome-container .welcome-input-container .welcome-input-wrapper-large {
  display: flex !important;
  flex-direction: column !important;
  background: #ffffff !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 24px !important;
  padding: 24px 32px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 32px !important;
  min-height: 120px !important;
  max-width: 100% !important;
  position: relative !important;
}

/* Beautiful Focus and Hover Effects - Welcome Screen Only */
.welcome-view .welcome-input-wrapper-large:focus-within {
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.welcome-view .welcome-input-wrapper-large:hover {
  border-color: #d1d5db;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Beautiful Input Styling - Welcome Screen Only */
.welcome-view .chat-input-large {
  width: 100%;
  border: none;
  outline: none;
  font-size: 18px;
  color: #1a202c;
  background: transparent;
  padding: 16px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'Roboto', sans-serif;
  font-weight: 400;
  resize: none;
  min-height: 48px;
  max-height: 200px;
  line-height: 1.6;
  margin-bottom: 20px;
  overflow-y: auto;
  overflow-x: hidden;
}

.welcome-view .chat-input-large::placeholder {
  color: #6b7280;
  font-size: 18px;
  font-weight: 400;
  opacity: 0.8;
}

/* Beautiful Tools Section - Welcome Screen Only */
.welcome-view .welcome-tools-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.welcome-view .left-tools,
.welcome-view .right-tools {
  display: flex;
  align-items: center;
  gap: 12px;
}

.welcome-view .tool-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: none;
  color: #6b7280;
  font-size: 16px;
  padding: 8px 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.welcome-view .tool-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Beautiful Send Button - Welcome Screen Only */
.welcome-view .send-btn {
  background: #000000 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  padding: 0 !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.welcome-view .send-btn:hover:not(:disabled) {
  background: #333333 !important;
  transform: scale(1.05);
}

.welcome-view .send-btn:disabled {
  background: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Mobile Responsive - Welcome Screen Only */
@media (max-width: 768px) {
  .welcome-view .brand-title {
    font-size: 36px;
    margin-bottom: 24px;
    line-height: 1.3;
    /* Maintain beautiful gradient on mobile */
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-view .welcome-input-wrapper-large {
    padding: 18px 24px;
    min-height: 85px;
    border-radius: 24px;
    max-width: 96vw;
    width: 96%;
  }

  .welcome-view .chat-input-large {
    font-size: 16px;
    padding: 12px 0;
    min-height: 40px;
    margin-bottom: 12px;
  }

  .welcome-view .chat-input-large::placeholder {
    font-size: 16px;
  }

  .welcome-view .left-tools,
  .welcome-view .right-tools {
    gap: 8px;
  }

  .welcome-view .tool-btn {
    padding: 6px 10px;
    font-size: 14px;
  }
}
