import { useState } from 'react';
import emailService from '../../services/emailService';
import './EmailReportModal.css';

export default function EmailReportModal({ 
  isOpen, 
  onClose, 
  message, 
  userEmail, 
  userName 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [error, setError] = useState('');
  const [customMessage, setCustomMessage] = useState('');

  const handleSendEmail = async () => {
    if (!userEmail) {
      setError('User email not available');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Prepare email data
      const emailData = {
        to: userEmail,
        subject: 'Report from Report Manager',
        reportContent: message.text,
        reportTimestamp: message.timestamp,
        customMessage: customMessage.trim(),
        userName: userName || 'User',
        downloadInfo: message.downloadInfo
      };

      console.log('Sending email with data:', emailData);

      // Use email service to send the email
      const result = await emailService.sendReportEmail(emailData);

      if (result.success) {
        setEmailSent(true);
        setTimeout(() => {
          onClose();
          setEmailSent(false);
          setCustomMessage('');
        }, 2000);
      } else {
        setError(result.message || 'Failed to send email');
      }
    } catch (error) {
      console.error('Email sending error:', error);
      setError(error.message || 'Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setError('');
      setEmailSent(false);
      setCustomMessage('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="email-modal-overlay" onClick={handleClose}>
      <div className="email-modal" onClick={(e) => e.stopPropagation()}>
        <div className="email-modal-header">
          <h3>📧 Email Report</h3>
          <button 
            className="close-btn" 
            onClick={handleClose}
            disabled={isLoading}
          >
            ✕
          </button>
        </div>

        <div className="email-modal-content">
          {emailSent ? (
            <div className="success-message">
              <div className="success-icon">✅</div>
              <h4>Email Sent Successfully!</h4>
              <p>The report has been sent to {userEmail}</p>
            </div>
          ) : (
            <>
              <div className="email-info">
                <div className="email-field">
                  <label>To:</label>
                  <span className="email-address">{userEmail || 'No email available'}</span>
                </div>
                <div className="email-field">
                  <label>Subject:</label>
                  <span>Report from Report Manager</span>
                </div>
              </div>

              <div className="report-preview">
                <label>Report Preview:</label>
                <div className="report-content">
                  <div className="report-text">
                    {message.text || 'No report content available'}
                  </div>
                  {message.downloadInfo && (
                    <div className="attachment-section">
                      <div className="attachment-info">
                        📎 <strong>Attachment:</strong> {message.downloadInfo.filename}
                      </div>
                      <div className="attachment-details">
                        <span>Size: {message.downloadInfo.size || 'Unknown'}</span>
                        <span>Type: {message.downloadInfo.type || 'CSV'}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="custom-message-section">
                <label htmlFor="customMessage">Add a personal message (optional):</label>
                <textarea
                  id="customMessage"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Add any additional notes or context..."
                  rows={3}
                  maxLength={500}
                  disabled={isLoading}
                />
                <div className="char-count">
                  {customMessage.length}/500
                </div>
              </div>

              {error && (
                <div className="error-message">
                  ⚠️ {error}
                </div>
              )}
            </>
          )}
        </div>

        {!emailSent && (
          <div className="email-modal-footer">
            <button 
              className="cancel-btn" 
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              className="send-btn"
              onClick={handleSendEmail}
              disabled={isLoading || !userEmail}
            >
              {isLoading ? 'Sending...' : 'Send'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
