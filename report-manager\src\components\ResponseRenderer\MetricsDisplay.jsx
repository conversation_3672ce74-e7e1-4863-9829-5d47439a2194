import React from 'react';
import './MetricsDisplay.css';

const MetricsDisplay = ({ metrics }) => {
  if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
    return null;
  }

  const getTrendIcon = (trend) => {
    switch (trend?.toLowerCase()) {
      case 'up':
        return '📈';
      case 'down':
        return '📉';
      case 'stable':
        return '➡️';
      default:
        return '📊';
    }
  };

  const getTrendClass = (trend) => {
    switch (trend?.toLowerCase()) {
      case 'up':
        return 'trend-up';
      case 'down':
        return 'trend-down';
      case 'stable':
        return 'trend-stable';
      default:
        return 'trend-neutral';
    }
  };

  const formatValue = (value, name) => {
    if (typeof value === 'number') {
      if (name?.toLowerCase().includes('rate')) {
        return `${(value * 100).toFixed(1)}%`;
      }
      if (name?.toLowerCase().includes('revenue')) {
        return `$${value.toLocaleString()}`;
      }
      return value.toLocaleString();
    }
    return String(value);
  };

  return (
    <div className="metrics-display">
      <h3 className="metrics-title">📊 Key Metrics</h3>
      <div className="metrics-grid">
        {metrics.map((metric, index) => (
          <div key={index} className={`metric-card ${getTrendClass(metric.trend)}`}>
            <div className="metric-header">
              <span className="metric-name">{metric.name}</span>
              <span className="metric-trend">
                {getTrendIcon(metric.trend)}
              </span>
            </div>
            <div className="metric-value">
              {formatValue(metric.value, metric.name)}
            </div>
            {metric.period && (
              <div className="metric-period">{metric.period}</div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MetricsDisplay;
