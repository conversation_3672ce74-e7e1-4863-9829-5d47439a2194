{"name": "report-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "axios": "^1.11.0", "chart.js": "^4.5.0", "dotenv": "^17.2.1", "exceljs": "^4.4.0", "google-auth-library": "^10.2.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-speech-recognition": "^4.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.0.0", "crypto-browserify": "^3.12.1", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^4.5.0"}}