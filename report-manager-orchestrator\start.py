#!/usr/bin/env python3
"""
Quick start script for Report Manager Orchestrator
"""

import sys
import os
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import loguru
        print("✅ Required packages found")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False

def check_config():
    """Check if configuration files exist"""
    config_files = [
        "config/database_config.yaml",
        ".env.example"
    ]
    
    missing_files = []
    for file_path in config_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing config files: {missing_files}")
        return False
    
    print("✅ Configuration files found")
    return True

def main():
    """Main startup function"""
    print("🚀 Report Manager Orchestrator - Startup Check")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check configuration
    if not check_config():
        print("💡 Please ensure all configuration files are present")
        sys.exit(1)
    
    # Check environment file
    if not Path(".env").exists():
        print("⚠️  .env file not found")
        print("💡 Copy .env.example to .env and configure your database")
        print("   cp .env.example .env")
        print()
    
    print("✅ All checks passed!")
    print()
    print("🎯 To start the server:")
    print("   python main.py")
    print()
    print("🌐 API will be available at:")
    print("   http://localhost:8000")
    print("   http://localhost:8000/docs (API documentation)")
    print()
    print("📝 Example usage:")
    print("   POST /query/excel")
    print("   Body: {\"text\": \"I need all einvoicing Tax code\"}")

if __name__ == "__main__":
    main()
