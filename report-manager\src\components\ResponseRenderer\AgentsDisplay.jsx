import React from 'react';
import './AgentsDisplay.css';

const AgentsDisplay = ({ agents, executionTime, queryType }) => {
  if (!agents || !Array.isArray(agents) || agents.length === 0) {
    return null;
  }

  const getAgentIcon = (agentName) => {
    const name = agentName.toLowerCase();
    if (name.includes('query')) return '🔍';
    if (name.includes('data')) return '📊';
    if (name.includes('report')) return '📋';
    if (name.includes('analysis')) return '🔬';
    if (name.includes('search')) return '🔎';
    if (name.includes('processing')) return '⚙️';
    return '🤖';
  };

  const formatExecutionTime = (time) => {
    if (!time) return '';
    if (time < 1) return `${(time * 1000).toFixed(0)}ms`;
    return `${time.toFixed(2)}s`;
  };

  const formatAgentName = (name) => {
    return name
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="agents-display">
      <div className="agents-header">
        <span className="agents-title">🤖 Processing Details</span>
      </div>
      
      <div className="agents-content">
        <div className="agents-list">
          <span className="agents-label">Agents Used:</span>
          <div className="agents-tags">
            {agents.map((agent, index) => (
              <span key={index} className="agent-tag">
                <span className="agent-icon">{getAgentIcon(agent)}</span>
                <span className="agent-name">{formatAgentName(agent)}</span>
              </span>
            ))}
          </div>
        </div>
        
        <div className="processing-info">
          {executionTime && (
            <div className="info-item">
              <span className="info-icon">⏱️</span>
              <span className="info-label">Execution Time:</span>
              <span className="info-value">{formatExecutionTime(executionTime)}</span>
            </div>
          )}
          
          {queryType && (
            <div className="info-item">
              <span className="info-icon">🎯</span>
              <span className="info-label">Query Type:</span>
              <span className="info-value">{formatAgentName(queryType)}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentsDisplay;
