"""
SQL Agent for Text-to-SQL Conversion

This agent converts natural language queries into SQL statements
using database schema information and AI-powered parsing.
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from loguru import logger

from .base import BaseAgent
from ..core.context import User<PERSON>ontext, TaskType
from ..core.result import QueryResult
from ..database.config_manager import DatabaseConfigManager, DataSource
from ..database.schema_discovery import DynamicSchemaDiscovery


@dataclass
class SQLQuery:
    """Represents a generated SQL query with metadata"""
    sql: str
    datasource: str
    tables: List[str]
    columns: List[str]
    parameters: Dict[str, Any]
    confidence: float
    explanation: str


class SQLAgent(BaseAgent):
    """
    Agent responsible for converting natural language to SQL queries
    """
    
    def __init__(self, llm_client, config_manager: DatabaseConfigManager, schema_discovery: DynamicSchemaDiscovery):
        """
        Initialize the SQL agent

        Args:
            llm_client: Language model client for AI-powered parsing
            config_manager: Database configuration manager
            schema_discovery: Dynamic schema discovery service
        """
        super().__init__("sql_agent", ["QUERY", "DATA_ANALYSIS"])
        self.llm_client = llm_client
        self.config_manager = config_manager
        self.schema_discovery = schema_discovery
        
        # SQL generation patterns for natural language processing
        self.query_patterns = {
            'aggregation': ['total', 'sum', 'count', 'average', 'avg', 'max', 'min'],
            'filtering': ['where', 'filter', 'only', 'specific', 'particular'],
            'grouping': ['by', 'group', 'category', 'department', 'type'],
            'sorting': ['top', 'bottom', 'highest', 'lowest', 'best', 'worst', 'order'],
            'time_based': ['today', 'yesterday', 'week', 'month', 'year', 'quarter', 'daily', 'monthly']
        }
        
        logger.info("SQL Agent initialized")
    
    async def process(self, context: UserContext) -> QueryResult:
        """
        Process a user query and convert it to SQL
        
        Args:
            context: User context containing the query
            
        Returns:
            QueryResult with generated SQL query
        """
        try:
            logger.info(f"SQL Agent processing query: {context.intent}")
            
            # Find relevant data sources and tables
            relevant_datasources = self.config_manager.find_relevant_datasources(context.intent)
            
            if not relevant_datasources:
                return QueryResult(
                    success=False,
                    error="No relevant data sources found for this query",
                    query_type="sql_generation"
                )
            
            # Generate SQL for the most relevant data source
            primary_datasource = relevant_datasources[0]
            relevant_tables = self.config_manager.find_relevant_tables(context.intent, primary_datasource)
            
            if not relevant_tables:
                # Use all tables if no specific tables identified
                datasource = self.config_manager.get_data_source(primary_datasource)
                relevant_tables = list(datasource.tables.keys()) if datasource else []
            
            # Generate SQL query
            sql_query = await self._generate_sql_query(
                context.intent, 
                primary_datasource, 
                relevant_tables
            )
            
            if not sql_query:
                return QueryResult(
                    success=False,
                    error="Failed to generate SQL query",
                    query_type="sql_generation"
                )
            
            return QueryResult(
                success=True,
                data={
                    "sql_query": sql_query.sql,
                    "datasource": sql_query.datasource,
                    "tables": sql_query.tables,
                    "columns": sql_query.columns,
                    "parameters": sql_query.parameters,
                    "confidence": sql_query.confidence,
                    "explanation": sql_query.explanation
                },
                query_type="sql_generation",
                metadata={
                    "relevant_datasources": relevant_datasources,
                    "relevant_tables": relevant_tables
                }
            )
            
        except Exception as e:
            logger.error(f"Error in SQL Agent: {e}")
            return QueryResult(
                success=False,
                error=str(e),
                query_type="sql_generation"
            )
    
    async def _generate_sql_query(self, query_text: str, datasource_name: str, tables: List[str]) -> Optional[SQLQuery]:
        """
        Generate SQL query using AI and pattern matching
        
        Args:
            query_text: Natural language query
            datasource_name: Target data source
            tables: Relevant tables
            
        Returns:
            Generated SQL query or None if failed
        """
        try:
            # Get data source and table information
            datasource = self.config_manager.get_data_source(datasource_name)
            if not datasource:
                logger.error(f"Data source not found: {datasource_name}")
                return None

            # Discover actual schema (especially important for Excel files)
            discovered_schema = await self.schema_discovery.discover_schema(datasource_name)

            # Build schema context for AI using discovered schema
            schema_context = self._build_schema_context_from_discovery(datasource, discovered_schema, tables)
            
            # Check for predefined query templates first
            template_query = self._check_query_templates(query_text, datasource_name)
            if template_query:
                return template_query
            
            # Use AI to generate SQL
            ai_query = await self._generate_ai_sql(query_text, schema_context, datasource_name, tables)
            if ai_query:
                return ai_query
            
            # Fallback to pattern-based generation
            pattern_query = self._generate_pattern_sql(query_text, datasource, tables)
            return pattern_query
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {e}")
            return None
    
    def _build_schema_context(self, datasource: DataSource, tables: List[str]) -> str:
        """
        Build schema context for AI SQL generation
        
        Args:
            datasource: Data source information
            tables: List of relevant tables
            
        Returns:
            Schema context string
        """
        context_parts = [
            f"Database: {datasource.name} ({datasource.type})",
            f"Description: {datasource.description}",
            "",
            "Available Tables:"
        ]
        
        for table_name in tables:
            if table_name in datasource.tables:
                table_info = datasource.tables[table_name]
                context_parts.append(f"\nTable: {table_name}")
                context_parts.append(f"Description: {table_info.description}")
                context_parts.append("Columns:")
                
                for col_name, col_info in table_info.columns.items():
                    context_parts.append(f"  - {col_name} ({col_info.type}): {col_info.description}")
        
        return "\n".join(context_parts)

    def _build_schema_context_from_discovery(self, datasource: DataSource, discovered_schema: dict, tables: List[str]) -> str:
        """
        Build schema context for AI SQL generation using discovered schema

        Args:
            datasource: Data source information
            discovered_schema: Discovered schema from schema discovery
            tables: List of relevant tables

        Returns:
            Schema context string
        """
        context_parts = [
            f"Database: {datasource.name} ({datasource.type})",
            f"Description: {datasource.description}",
            "",
            "Available Tables:"
        ]

        # Use discovered schema if available, otherwise fall back to static config
        if discovered_schema:
            for table_name, table_info in discovered_schema.items():
                if not tables or table_name in tables:
                    context_parts.append(f"\nTable: {table_name}")
                    context_parts.append(f"Description: {table_info.description or 'No description'}")
                    context_parts.append("Columns:")

                    for col_name, col_info in table_info.columns.items():
                        context_parts.append(f"  - {col_name} ({col_info.type}): {col_info.description or 'No description'}")
        else:
            # Fallback to static configuration
            for table_name in tables:
                if table_name in datasource.tables:
                    table_info = datasource.tables[table_name]
                    context_parts.append(f"\nTable: {table_name}")
                    context_parts.append(f"Description: {table_info.description}")
                    context_parts.append("Columns:")

                    for col_name, col_info in table_info.columns.items():
                        context_parts.append(f"  - {col_name} ({col_info.type}): {col_info.description}")

        return "\n".join(context_parts)

    def _check_query_templates(self, query_text: str, datasource_name: str) -> Optional[SQLQuery]:
        """
        Check if query matches predefined templates
        
        Args:
            query_text: Natural language query
            datasource_name: Target data source
            
        Returns:
            SQL query if template matches, None otherwise
        """
        query_lower = query_text.lower()
        templates = self.config_manager.get_query_templates()
        
        for template_name, template_config in templates.items():
            if template_config.get('datasource') != datasource_name:
                continue
            
            # Simple keyword matching for templates
            template_keywords = template_name.replace('_', ' ').split()
            if all(keyword in query_lower for keyword in template_keywords):
                sql = template_config.get('sql', '')
                
                # Extract parameters (simplified)
                parameters = {}
                for param in template_config.get('parameters', []):
                    if param == 'limit':
                        # Extract number from query
                        numbers = re.findall(r'\d+', query_text)
                        parameters[param] = numbers[0] if numbers else '10'
                    elif param in ['start_date', 'end_date']:
                        # For demo, use default dates
                        parameters[param] = '2024-01-01' if 'start' in param else '2024-12-31'
                
                # Replace parameters in SQL
                formatted_sql = sql.format(**parameters)
                
                return SQLQuery(
                    sql=formatted_sql,
                    datasource=datasource_name,
                    tables=[],  # Extract from SQL if needed
                    columns=[],
                    parameters=parameters,
                    confidence=0.9,
                    explanation=f"Used predefined template: {template_name}"
                )
        
        return None
    
    async def _generate_ai_sql(self, query_text: str, schema_context: str, datasource_name: str, tables: List[str]) -> Optional[SQLQuery]:
        """
        Generate SQL using AI language model - PURE AI, NO HARDCODING

        Args:
            query_text: Natural language query
            schema_context: Database schema information
            datasource_name: Target data source
            tables: Relevant tables

        Returns:
            Generated SQL query or None
        """
        try:
            logger.info(f"🤖 Using PURE AI to generate SQL for: {query_text}")

            # Construct prompt for AI - NO HARDCODED PATTERNS!
            prompt = f"""
Convert the following natural language query to SQL based on the provided database schema.

Database Schema:
{schema_context}

Natural Language Query: {query_text}

Instructions:
- Generate only SELECT statements
- Use proper table names from the schema
- Include appropriate WHERE clauses for filtering
- Use LIKE operator for pattern matching (e.g., 'R%' for starts with R)
- Use aggregate functions when asking for totals, counts, etc.
- For date/time queries, use appropriate date functions
- Return only the SQL query, no explanations or formatting

SQL Query:
"""

            # Call AI model
            logger.info(f"🤖 Sending prompt to AI for datasource: {datasource_name}")
            response = await self.llm_client.generate_response(prompt)

            if not response or not response.strip():
                logger.warning("AI returned empty SQL response")
                return None

            # Clean and validate SQL
            sql = self._clean_sql(response)
            logger.info(f"🤖 AI Generated SQL: {sql}")

            if not self._validate_sql(sql):
                logger.warning(f"Generated SQL failed validation: {sql}")
                return None

            return SQLQuery(
                sql=sql,
                datasource=datasource_name,
                tables=tables,
                columns=self._extract_columns_from_sql(sql),
                parameters={},
                confidence=0.9,
                explanation="Generated using AI language model - NO HARDCODING"
            )

        except Exception as e:
            logger.error(f"Error in AI SQL generation: {e}")
            return None
    
    def _generate_pattern_sql(self, query_text: str, datasource: DataSource, tables: List[str]) -> Optional[SQLQuery]:
        """
        Generate SQL using pattern matching as fallback
        
        Args:
            query_text: Natural language query
            datasource: Data source information
            tables: Relevant tables
            
        Returns:
            Generated SQL query or None
        """
        try:
            query_lower = query_text.lower()
            
            # Default to first table if available
            if not tables:
                return None
            
            primary_table = tables[0]
            table_info = datasource.tables.get(primary_table)
            
            if not table_info:
                return None
            
            # Build basic SELECT
            columns = ["*"]  # Default to all columns
            
            # Check for specific column requests
            for col_name in table_info.columns.keys():
                if col_name.lower() in query_lower:
                    if "*" in columns:
                        columns = []
                    columns.append(col_name)
            
            # Build WHERE clause
            where_conditions = []
            
            # Check for aggregation
            aggregation = None
            for agg_keyword in self.query_patterns['aggregation']:
                if agg_keyword in query_lower:
                    if agg_keyword in ['total', 'sum']:
                        # Find numeric columns
                        numeric_cols = [col for col, info in table_info.columns.items() 
                                      if info.type in ['integer', 'decimal', 'float']]
                        if numeric_cols:
                            aggregation = f"SUM({numeric_cols[0]})"
                            columns = [f"SUM({numeric_cols[0]}) as total"]
                    elif agg_keyword in ['count']:
                        aggregation = "COUNT(*)"
                        columns = ["COUNT(*) as count"]
                    break
            
            # Build SQL
            sql_parts = [f"SELECT {', '.join(columns)}"]
            sql_parts.append(f"FROM {primary_table}")
            
            if where_conditions:
                sql_parts.append(f"WHERE {' AND '.join(where_conditions)}")
            
            # Add LIMIT for safety
            sql_parts.append("LIMIT 100")
            
            sql = " ".join(sql_parts)
            
            return SQLQuery(
                sql=sql,
                datasource=datasource.name,
                tables=[primary_table],
                columns=columns,
                parameters={},
                confidence=0.6,
                explanation="Generated using pattern matching"
            )
            
        except Exception as e:
            logger.error(f"Error in pattern SQL generation: {e}")
            return None
    
    def _clean_sql(self, sql: str) -> str:
        """Clean and format SQL query"""
        # Remove markdown code blocks if present
        sql = re.sub(r'```sql\s*', '', sql)
        sql = re.sub(r'```\s*', '', sql)
        
        # Remove extra whitespace
        sql = ' '.join(sql.split())

        # Remove semicolon to avoid security issues
        sql = sql.rstrip(';')

        return sql
    
    def _validate_sql(self, sql: str) -> bool:
        """Basic SQL validation"""
        sql_upper = sql.upper()
        
        # Must start with SELECT
        if not sql_upper.strip().startswith('SELECT'):
            return False
        
        # Check for dangerous keywords
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                return False
        
        # Basic syntax check
        if 'FROM' not in sql_upper:
            return False
        
        return True
    
    def _extract_columns_from_sql(self, sql: str) -> List[str]:
        """Extract column names from SQL query"""
        try:
            # Simple regex to extract columns from SELECT clause
            select_match = re.search(r'SELECT\s+(.*?)\s+FROM', sql, re.IGNORECASE)
            if select_match:
                columns_str = select_match.group(1)
                if columns_str.strip() == '*':
                    return ['*']
                
                # Split by comma and clean
                columns = [col.strip() for col in columns_str.split(',')]
                return columns
        except Exception as e:
            logger.warning(f"Error extracting columns from SQL: {e}")
        
        return []
