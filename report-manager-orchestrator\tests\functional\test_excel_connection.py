#!/usr/bin/env python3
"""
Test Excel Database Connection and Schema Discovery
"""

import asyncio
import os
from src.report_manager.database.query_engine import DatabaseQueryEngine

async def test_excel_connection():
    """Test Excel database connection and schema discovery"""
    
    print("🧪 Testing Excel Database Connection")
    print("=" * 50)
    
    # Create a mock LLM client
    class MockLLMClient:
        async def generate_response(self, prompt, **kwargs):
            return "Mock response"
    
    try:
        # Initialize database query engine
        print("\n🔧 Initializing database query engine...")
        db_query_engine = DatabaseQueryEngine(MockLLMClient())
        
        # Initialize the engine
        print("🔧 Initializing database connections...")
        await db_query_engine.initialize()
        
        # Check connection status
        print("\n📊 Connection Status:")
        status = db_query_engine.connection_manager.get_connection_status()
        print(f"   Total connections: {status.get('total_connections')}")
        print(f"   SQL connections: {status.get('sql_connections')}")
        print(f"   Excel connections: {status.get('excel_connections')}")
        
        # List all connections
        print("\n🔗 All Connections:")
        for name, info in status.get('connections', {}).items():
            print(f"   {name}: {info.get('type')}")
            if info.get('type') == 'excel':
                print(f"      File: {info.get('file_path')}")
                print(f"      Sheet: {info.get('sheet_name')}")
                print(f"      Rows: {info.get('rows')}")
                print(f"      Columns: {info.get('columns')}")
        
        # Test schema discovery for Excel
        print("\n🔍 Testing Excel Schema Discovery:")
        excel_schema = await db_query_engine.schema_discovery.discover_schema('employee_db')
        
        if excel_schema:
            print(f"   ✅ Excel schema discovered!")
            for table_name, table_info in excel_schema.items():
                print(f"   📋 Table: {table_name}")
                print(f"      Type: {table_info.table_type}")
                print(f"      Rows: {table_info.row_count}")
                print(f"      Columns: {len(table_info.columns)}")
                print(f"      Sample columns: {list(table_info.columns.keys())[:5]}")
        else:
            print("   ❌ No Excel schema discovered")
        
        # Test available data sources
        print("\n📚 Available Data Sources:")
        data_sources = db_query_engine.config_manager.get_all_data_sources()
        for ds_name, ds_info in data_sources.items():
            print(f"   {ds_name}: {ds_info.type} - {ds_info.description}")
        
        # Create a mock session
        class MockSession:
            def __init__(self):
                self.user_id = "system"
                self.role = "admin"

        session = MockSession()
        
        # Test simple Excel query
        print("\n🧪 Testing Simple Excel Query:")
        try:
            result = await db_query_engine.execute_natural_language_query(
                "SELECT * FROM Employees LIMIT 5", 
                session
            )
            
            if result.success:
                print(f"   ✅ Direct SQL query success!")
                print(f"   📊 Rows: {len(result.data) if result.data else 0}")
                print(f"   🗄️ Datasource: {result.datasource}")
            else:
                print(f"   ❌ Direct SQL query failed: {result.error}")
                
        except Exception as e:
            print(f"   ❌ Exception in direct query: {e}")
        
        print("\n" + "=" * 50)
        print("🎯 Excel Connection Test Complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_excel_connection())
