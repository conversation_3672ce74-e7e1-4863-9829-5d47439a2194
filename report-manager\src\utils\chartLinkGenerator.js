/**
 * Chart Link Generator - Creates shareable browser links for charts
 */

class ChartLinkGenerator {
  /**
   * Generate a shareable browser link for a chart
   * @param {Object} chartData - Chart data object
   * @param {string} baseUrl - Base URL of the application
   * @returns {Object} Chart link information
   */
  static generateChartLink(chartData, baseUrl = 'http://localhost:5173') {
    try {
      console.log('🔗 Generating shareable chart link...');
      
      if (!chartData || !chartData.data) {
        throw new Error('Invalid chart data provided');
      }
      
      // Create a unique chart ID
      const chartId = 'chart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // Encode chart data for URL
      const encodedChartData = btoa(JSON.stringify({
        id: chartId,
        type: chartData.type || 'donut',
        title: chartData.title || 'Chart',
        data: chartData.data,
        options: chartData.options || {},
        timestamp: new Date().toISOString(),
        generated_by: 'Report Manager'
      }));
      
      // Create shareable URL
      const chartUrl = `${baseUrl}/chart/${chartId}?data=${encodedChartData}`;
      
      // Create QR code data (simple text representation for now)
      const qrCodeText = `QR: ${chartUrl}`;
      
      console.log('✅ Chart link generated successfully');
      
      return {
        chartId,
        url: chartUrl,
        shortUrl: `${baseUrl}/c/${chartId}`, // Shorter version
        qrCode: qrCodeText,
        title: chartData.title || 'Chart',
        type: chartData.type || 'donut',
        timestamp: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        metadata: {
          dataPoints: chartData.data?.labels?.length || 0,
          totalValue: chartData.data?.values?.reduce((a, b) => a + b, 0) || 0,
          categories: chartData.data?.labels || []
        }
      };
      
    } catch (error) {
      console.error('❌ Error generating chart link:', error);
      throw error;
    }
  }
  
  /**
   * Generate chart link only when chart keywords are detected
   * @param {string} userQuery - User's original query
   * @param {Object} chartData - Chart data object
   * @param {string} baseUrl - Base URL of the application
   * @returns {Object|null} Chart link information or null if no chart requested
   */
  static generateChartLinkIfRequested(userQuery, chartData, baseUrl = 'http://localhost:5173') {
    try {
      // Chart keywords to detect
      const chartKeywords = [
        'chart', 'graph', 'plot', 'visualization', 'visual',
        'donut', 'pie', 'bar', 'line', 'scatter',
        'show me', 'display', 'visualize', 'draw'
      ];
      
      // Check if user query contains chart-related keywords
      const queryLower = userQuery.toLowerCase();
      const hasChartKeyword = chartKeywords.some(keyword => 
        queryLower.includes(keyword)
      );
      
      if (!hasChartKeyword) {
        console.log('🔍 No chart keywords detected in query, skipping chart link generation');
        return null;
      }
      
      console.log('🎯 Chart keywords detected, generating chart link...');
      return this.generateChartLink(chartData, baseUrl);
      
    } catch (error) {
      console.error('❌ Error in chart link detection:', error);
      return null;
    }
  }
  
  /**
   * Create chart link HTML for embedding in Excel or other documents
   * @param {Object} chartLinkInfo - Chart link information
   * @returns {string} HTML representation of the chart link
   */
  static createChartLinkHTML(chartLinkInfo) {
    if (!chartLinkInfo) return '';
    
    return `
      <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">
        <h3 style="margin: 0 0 10px 0; color: #2E86AB;">🔗 Interactive Chart Link</h3>
        <p><strong>Title:</strong> ${chartLinkInfo.title}</p>
        <p><strong>Type:</strong> ${chartLinkInfo.type.toUpperCase()}</p>
        <p><strong>Data Points:</strong> ${chartLinkInfo.metadata.dataPoints}</p>
        <p><strong>Link:</strong> <a href="${chartLinkInfo.url}" target="_blank">${chartLinkInfo.shortUrl}</a></p>
        <p><strong>Generated:</strong> ${new Date(chartLinkInfo.timestamp).toLocaleString()}</p>
        <p><strong>Expires:</strong> ${new Date(chartLinkInfo.expiresAt).toLocaleString()}</p>
        <p style="font-size: 12px; color: #666;">Click the link above to view the interactive chart in your browser</p>
      </div>
    `;
  }
  
  /**
   * Create chart link text for Excel cells
   * @param {Object} chartLinkInfo - Chart link information
   * @returns {Array} Array of text rows for Excel
   */
  static createChartLinkForExcel(chartLinkInfo) {
    if (!chartLinkInfo) return [];
    
    return [
      ['🔗 INTERACTIVE CHART LINK', ''],
      ['', ''],
      ['Title:', chartLinkInfo.title],
      ['Type:', chartLinkInfo.type.toUpperCase()],
      ['Data Points:', chartLinkInfo.metadata.dataPoints.toString()],
      ['Total Value:', chartLinkInfo.metadata.totalValue.toLocaleString()],
      ['', ''],
      ['Browser Link:', chartLinkInfo.url],
      ['Short Link:', chartLinkInfo.shortUrl],
      ['', ''],
      ['Generated:', new Date(chartLinkInfo.timestamp).toLocaleString()],
      ['Expires:', new Date(chartLinkInfo.expiresAt).toLocaleString()],
      ['', ''],
      ['📱 INSTRUCTIONS:', ''],
      ['1. Click or copy the link above', ''],
      ['2. Open in any web browser', ''],
      ['3. View interactive chart with tooltips', ''],
      ['4. Share link with others', ''],
      ['5. Link expires in 30 days', ''],
      ['', ''],
      ['💡 TIP: Bookmark the link for easy access!', '']
    ];
  }
  
  /**
   * Store chart data temporarily (in production, this would use a database)
   * @param {string} chartId - Chart ID
   * @param {Object} chartData - Chart data to store
   */
  static storeChartData(chartId, chartData) {
    try {
      // In a real application, this would store in a database
      // For now, we'll use localStorage as a demo
      const storageKey = `chart_${chartId}`;
      const chartInfo = {
        id: chartId,
        data: chartData,
        timestamp: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      };
      
      localStorage.setItem(storageKey, JSON.stringify(chartInfo));
      console.log(`📦 Chart data stored with ID: ${chartId}`);
      
    } catch (error) {
      console.error('❌ Error storing chart data:', error);
    }
  }
  
  /**
   * Retrieve chart data by ID
   * @param {string} chartId - Chart ID
   * @returns {Object|null} Chart data or null if not found/expired
   */
  static retrieveChartData(chartId) {
    try {
      const storageKey = `chart_${chartId}`;
      const storedData = localStorage.getItem(storageKey);
      
      if (!storedData) {
        console.log(`📦 No chart data found for ID: ${chartId}`);
        return null;
      }
      
      const chartInfo = JSON.parse(storedData);
      
      // Check if expired
      if (new Date() > new Date(chartInfo.expiresAt)) {
        console.log(`⏰ Chart data expired for ID: ${chartId}`);
        localStorage.removeItem(storageKey);
        return null;
      }
      
      console.log(`📦 Chart data retrieved for ID: ${chartId}`);
      return chartInfo.data;
      
    } catch (error) {
      console.error('❌ Error retrieving chart data:', error);
      return null;
    }
  }
}

export default ChartLinkGenerator;
