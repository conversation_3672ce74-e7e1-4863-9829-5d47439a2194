import React, { useState, useEffect, useRef } from 'react';
import './ProgressOverlay.css';

/**
 * Thinking Message Component (ChatGPT-style)
 * Shows "Thinking longer for a better answer" with Skip option
 */
const ProgressOverlay = ({
  isVisible = false,
  onSkip = null,
  onComplete = null,
  enableArtificialDelay = true,
  totalDelayMs = 13000,
  message = "Thinking longer for a better answer"
}) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const [startTime, setStartTime] = useState(null);

  const timerRef = useRef(null);
  const skipButtonRef = useRef(null);

  useEffect(() => {
    if (isVisible && enableArtificialDelay && !isCompleted && !isCancelled) {
      const now = Date.now();
      setStartTime(now);

      console.log('🤔 Thinking message started:', {
        startTime: now,
        totalDelayMs
      });

      // Focus the skip button for accessibility
      if (skipButtonRef.current) {
        skipButtonRef.current.focus();
      }

      // Set completion timer
      timerRef.current = setTimeout(() => {
        if (!isCancelled) {
          const endTime = Date.now();
          console.log('✅ Thinking completed:', {
            startTime: now,
            endTime,
            totalDuration: endTime - now
          });

          setIsCompleted(true);

          // Complete immediately
          if (onComplete) {
            onComplete();
          }
        }
      }, totalDelayMs);
    }

    return () => {
      // Cleanup timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isVisible, enableArtificialDelay, totalDelayMs, isCompleted, isCancelled, onComplete]);

  // Handle skip
  const handleSkip = () => {
    const skipTime = Date.now();
    setIsCancelled(true);

    console.log('⏭️ Thinking skipped:', {
      startTime,
      skipTime,
      elapsed: startTime ? skipTime - startTime : 0
    });

    // Clear timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    if (onSkip) {
      onSkip();
    }
  };

  if (!isVisible || isCancelled || isCompleted) {
    return null;
  }

  return (
    <div className="thinking-container">
      <div className="thinking-content">
        <div className="thinking-text">
          {message}
        </div>
        {onSkip && (
          <button
            ref={skipButtonRef}
            className="skip-button"
            onClick={handleSkip}
            aria-label="Skip thinking and show response now"
          >
            Skip &gt;
          </button>
        )}
      </div>
    </div>
  );
};

export default ProgressOverlay;
